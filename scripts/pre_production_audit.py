#!/usr/bin/env python3
"""
Pre-Production Audit Script
Comprehensive validation of the Apple ML Trading codebase before 8-hour production session
"""

import os
import sys
import json
import time
import shutil
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class PreProductionAuditor:
    """Comprehensive pre-production audit system"""
    
    def __init__(self):
        self.project_root = project_root
        self.audit_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'PENDING',
            'sections': {},
            'issues_found': [],
            'recommendations': [],
            'go_no_go': 'PENDING'
        }
        self.logger = self._setup_logger()
        
        # Expected directory structure
        self.expected_structure = {
            'src': ['data_pipeline', 'feature_engineering', 'models', 'backtesting', 'risk_metrics', 'utils'],
            'config': ['data_sources', 'pipelines', 'models', 'environments', 'schemas'],
            'data': ['raw', 'processed', 'features', 'models', 'exports'],
            'scripts': [],
            'docs': ['architecture', 'data_sources', 'orchestrator'],
            'tests': []
        }
    
    def _setup_logger(self) -> logging.Logger:
        """Setup audit logging"""
        logger = logging.getLogger('PreProductionAudit')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - AUDIT - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # File handler
            audit_log = self.project_root / f"pre_production_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            file_handler = logging.FileHandler(audit_log)
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def run_comprehensive_audit(self) -> Dict[str, Any]:
        """Run the complete pre-production audit"""
        self.logger.info("🚀 Starting Comprehensive Pre-Production Audit")
        self.logger.info("=" * 60)
        
        audit_sections = [
            ("1. Codebase Organization & Structure", self._audit_codebase_structure),
            ("2. System Integration Testing", self._audit_system_integration),
            ("3. API Key & Configuration", self._audit_api_configuration),
            ("4. Data Pipeline Validation", self._audit_data_pipeline),
            ("5. Production Readiness", self._audit_production_readiness),
            ("6. Performance & Capacity", self._audit_performance_capacity)
        ]
        
        for section_name, audit_function in audit_sections:
            self.logger.info(f"\n📊 {section_name}")
            self.logger.info("-" * 50)
            
            try:
                section_result = audit_function()
                self.audit_results['sections'][section_name] = section_result
                
                if section_result['status'] == 'PASS':
                    self.logger.info(f"✅ {section_name}: PASSED")
                elif section_result['status'] == 'WARN':
                    self.logger.warning(f"⚠️ {section_name}: WARNINGS")
                else:
                    self.logger.error(f"❌ {section_name}: FAILED")
                
            except Exception as e:
                self.logger.error(f"❌ {section_name}: EXCEPTION - {str(e)}")
                self.audit_results['sections'][section_name] = {
                    'status': 'FAIL',
                    'error': str(e),
                    'details': {}
                }
        
        # Generate final assessment
        self._generate_final_assessment()
        
        # Save audit report
        self._save_audit_report()
        
        return self.audit_results
    
    def _audit_codebase_structure(self) -> Dict[str, Any]:
        """Audit 1: Codebase Organization & Structure Validation"""
        result = {
            'status': 'PASS',
            'details': {
                'directory_structure': {},
                'file_organization': {},
                'naming_conventions': {},
                'duplicates_found': [],
                'orphaned_files': []
            },
            'issues': [],
            'recommendations': []
        }
        
        # Check main directory structure
        for main_dir, expected_subdirs in self.expected_structure.items():
            main_path = self.project_root / main_dir
            
            if not main_path.exists():
                result['issues'].append(f"Missing main directory: {main_dir}")
                result['status'] = 'FAIL'
                continue
            
            result['details']['directory_structure'][main_dir] = {
                'exists': True,
                'subdirs_found': [],
                'subdirs_missing': []
            }
            
            # Check subdirectories
            for subdir in expected_subdirs:
                subdir_path = main_path / subdir
                if subdir_path.exists():
                    result['details']['directory_structure'][main_dir]['subdirs_found'].append(subdir)
                else:
                    result['details']['directory_structure'][main_dir]['subdirs_missing'].append(subdir)
                    result['issues'].append(f"Missing subdirectory: {main_dir}/{subdir}")
                    if result['status'] == 'PASS':
                        result['status'] = 'WARN'
        
        # Check for Python files in correct locations
        python_files = list(self.project_root.rglob("*.py"))
        result['details']['file_organization']['total_python_files'] = len(python_files)
        
        # Check for duplicates
        file_names = {}
        for py_file in python_files:
            name = py_file.name
            if name in file_names:
                file_names[name].append(str(py_file))
            else:
                file_names[name] = [str(py_file)]
        
        duplicates = {name: paths for name, paths in file_names.items() if len(paths) > 1}
        result['details']['duplicates_found'] = duplicates
        
        if duplicates:
            result['issues'].extend([f"Duplicate file: {name}" for name in duplicates.keys()])
            if result['status'] == 'PASS':
                result['status'] = 'WARN'
        
        # Check naming conventions
        naming_issues = []
        for py_file in python_files:
            if not py_file.name.islower() or ' ' in py_file.name:
                naming_issues.append(str(py_file))
        
        if naming_issues:
            result['details']['naming_conventions']['violations'] = naming_issues
            result['issues'].append(f"Naming convention violations: {len(naming_issues)} files")
            if result['status'] == 'PASS':
                result['status'] = 'WARN'
        
        return result
    
    def _audit_system_integration(self) -> Dict[str, Any]:
        """Audit 2: System Integration Testing"""
        result = {
            'status': 'PASS',
            'details': {
                'collectors_tested': {},
                'api_key_manager': {},
                'orchestrator': {},
                'integrator': {}
            },
            'issues': [],
            'recommendations': []
        }
        
        # Test individual collectors
        collectors_to_test = [
            ('eodhd', 'src.data_pipeline.collectors.eodhd_collector', 'EODHDCollector'),
            ('finnhub', 'src.data_pipeline.collectors.finnhub_collector', 'FinnhubCollector'),
            ('financial_modeling_prep', 'src.data_pipeline.collectors.financial_modeling_prep_collector', 'FinancialModelingPrepCollector'),
            ('yahoo_finance', 'scripts.create_yahoo_collector', 'YahooFinanceCollector'),
            ('alpha_vantage', 'src.data_pipeline.collectors.alpha_vantage_collector', 'AlphaVantageCollector')
        ]
        
        for collector_name, module_path, class_name in collectors_to_test:
            try:
                # Import the collector
                module = __import__(module_path, fromlist=[class_name])
                collector_class = getattr(module, class_name)
                
                # Initialize collector
                collector = collector_class()
                
                result['details']['collectors_tested'][collector_name] = {
                    'import_success': True,
                    'initialization_success': True,
                    'has_required_methods': self._check_collector_methods(collector)
                }
                
                self.logger.info(f"✅ {collector_name} collector: OK")
                
            except Exception as e:
                result['details']['collectors_tested'][collector_name] = {
                    'import_success': False,
                    'error': str(e)
                }
                result['issues'].append(f"{collector_name} collector failed: {str(e)}")
                result['status'] = 'FAIL'
                self.logger.error(f"❌ {collector_name} collector: {str(e)}")
        
        # Test MultiAPIKeyManager
        try:
            from src.data_pipeline.utils.multi_api_key_manager import MultiAPIKeyManager
            key_manager = MultiAPIKeyManager()
            
            # Test key retrieval for each source
            sources = ['eodhd', 'finnhub', 'financial_modeling_prep', 'yahoo_finance', 'alpha_vantage', 'polygon']
            key_status = {}
            
            for source in sources:
                available_keys = key_manager.get_available_keys(source)
                key_status[source] = len(available_keys)
            
            result['details']['api_key_manager'] = {
                'initialization_success': True,
                'key_availability': key_status,
                'total_active_keys': sum(key_status.values())
            }
            
            self.logger.info(f"✅ MultiAPIKeyManager: {sum(key_status.values())} active keys")
            
        except Exception as e:
            result['details']['api_key_manager'] = {'error': str(e)}
            result['issues'].append(f"MultiAPIKeyManager failed: {str(e)}")
            result['status'] = 'FAIL'
            self.logger.error(f"❌ MultiAPIKeyManager: {str(e)}")
        
        return result
    
    def _check_collector_methods(self, collector) -> Dict[str, bool]:
        """Check if collector has required methods"""
        required_methods = ['_make_request', '_setup_logger']
        optional_methods = ['collect_comprehensive_data', 'collect_batch_data']
        
        method_status = {}
        for method in required_methods + optional_methods:
            method_status[method] = hasattr(collector, method)
        
        return method_status
    
    def _audit_api_configuration(self) -> Dict[str, Any]:
        """Audit 3: API Key & Configuration"""
        result = {
            'status': 'PASS',
            'details': {
                'registry_validation': {},
                'active_keys': {},
                'rate_limits': {},
                'key_rotation': {}
            },
            'issues': [],
            'recommendations': []
        }
        
        # Load and validate registry
        registry_path = self.project_root / 'config' / 'data_sources' / 'registry.json'
        
        if not registry_path.exists():
            result['issues'].append("Data source registry not found")
            result['status'] = 'FAIL'
            return result

    def _audit_data_pipeline(self) -> Dict[str, Any]:
        """Audit 4: Data Pipeline Validation"""
        result = {
            'status': 'PASS',
            'details': {
                'schema_compliance': {},
                'data_saving': {},
                'quality_validation': {},
                'normalization': {}
            },
            'issues': [],
            'recommendations': []
        }

        # Test schema compliance
        try:
            schema_path = self.project_root / 'config' / 'schemas' / 'unified_schema.json'
            if schema_path.exists():
                with open(schema_path, 'r') as f:
                    schema = json.load(f)
                result['details']['schema_compliance']['schema_loaded'] = True
                result['details']['schema_compliance']['required_fields'] = len(schema.get('company_level', {}).get('required_fields', []))
            else:
                result['issues'].append("Unified schema file not found")
                if result['status'] == 'PASS':
                    result['status'] = 'WARN'
        except Exception as e:
            result['issues'].append(f"Schema validation failed: {str(e)}")
            result['status'] = 'FAIL'

        # Test data directories
        data_dirs = ['raw', 'processed', 'features', 'models', 'exports']
        for data_dir in data_dirs:
            dir_path = self.project_root / 'data' / data_dir
            if dir_path.exists():
                result['details']['data_saving'][data_dir] = {
                    'exists': True,
                    'writable': os.access(dir_path, os.W_OK),
                    'files_count': len(list(dir_path.rglob('*')))
                }
            else:
                result['issues'].append(f"Data directory missing: {data_dir}")
                if result['status'] == 'PASS':
                    result['status'] = 'WARN'

        # Test multi-source integrator
        try:
            from src.data_pipeline.integrators.multi_source_integrator import MultiSourceDataIntegrator
            integrator = MultiSourceDataIntegrator()

            result['details']['normalization']['integrator_loaded'] = True
            result['details']['normalization']['collectors_initialized'] = len(integrator.collectors)

            self.logger.info("✅ Data pipeline validation: OK")

        except Exception as e:
            result['issues'].append(f"Multi-source integrator failed: {str(e)}")
            result['status'] = 'FAIL'
            self.logger.error(f"❌ Data pipeline: {str(e)}")

        return result

    def _audit_production_readiness(self) -> Dict[str, Any]:
        """Audit 5: Production Readiness"""
        result = {
            'status': 'PASS',
            'details': {
                'launchers': {},
                'monitoring': {},
                'logging': {},
                'system_resources': {},
                'error_handling': {}
            },
            'issues': [],
            'recommendations': []
        }

        # Test production launchers
        launcher_scripts = [
            'scripts/launch_8_hour_collection.py',
            'scripts/monitor_collection_session.py',
            'scripts/manage_api_keys.py'
        ]

        for script in launcher_scripts:
            script_path = self.project_root / script
            if script_path.exists():
                result['details']['launchers'][script] = {
                    'exists': True,
                    'executable': os.access(script_path, os.X_OK),
                    'size_kb': script_path.stat().st_size / 1024
                }
            else:
                result['issues'].append(f"Missing launcher script: {script}")
                result['status'] = 'FAIL'

        # Test orchestrator
        try:
            from src.data_pipeline.orchestrators.comprehensive_data_orchestrator import ComprehensiveDataOrchestrator
            result['details']['monitoring']['orchestrator_importable'] = True
            self.logger.info("✅ Orchestrator import: OK")
        except Exception as e:
            result['issues'].append(f"Orchestrator import failed: {str(e)}")
            result['status'] = 'FAIL'
            self.logger.error(f"❌ Orchestrator: {str(e)}")

        # Check system resources
        disk_usage = shutil.disk_usage(self.project_root)
        free_gb = disk_usage.free / (1024**3)

        result['details']['system_resources'] = {
            'free_disk_gb': round(free_gb, 2),
            'sufficient_space': free_gb > 5.0,  # Need at least 5GB
            'python_version': sys.version,
            'platform': sys.platform
        }

        if free_gb < 5.0:
            result['issues'].append(f"Insufficient disk space: {free_gb:.1f}GB (need 5GB+)")
            result['status'] = 'FAIL'

        # Check logging directories
        log_dirs = ['data/orchestrator_sessions']
        for log_dir in log_dirs:
            log_path = self.project_root / log_dir
            log_path.mkdir(parents=True, exist_ok=True)
            result['details']['logging'][log_dir] = {
                'exists': log_path.exists(),
                'writable': os.access(log_path, os.W_OK)
            }

        return result

    def _audit_performance_capacity(self) -> Dict[str, Any]:
        """Audit 6: Performance & Capacity Verification"""
        result = {
            'status': 'PASS',
            'details': {
                'api_capacity': {},
                'rate_limiting': {},
                'performance_test': {},
                'capacity_calculations': {}
            },
            'issues': [],
            'recommendations': []
        }

        # Calculate API capacity
        api_limits = {
            'eodhd': {'daily': 20000, 'keys': 1},
            'finnhub': {'daily': 86400, 'keys': 1},  # 60/min * 1440 min
            'financial_modeling_prep': {'daily': 250, 'keys': 1},
            'yahoo_finance': {'daily': float('inf'), 'keys': 3},
            'alpha_vantage': {'daily': 500, 'keys': 1},
            'polygon': {'daily': 7200, 'keys': 0}  # 5/min * 1440 min
        }

        total_capacity = 0
        for source, limits in api_limits.items():
            if limits['daily'] != float('inf'):
                source_capacity = limits['daily'] * limits['keys']
                total_capacity += source_capacity
                result['details']['api_capacity'][source] = {
                    'daily_limit': limits['daily'],
                    'active_keys': limits['keys'],
                    'total_capacity': source_capacity
                }

        result['details']['capacity_calculations'] = {
            'total_api_capacity': total_capacity,
            'estimated_records': int(total_capacity * 0.8),  # 80% efficiency
            'meets_target': total_capacity >= 100000  # Target 100k+ requests
        }

        if total_capacity < 100000:
            result['recommendations'].append(f"Consider adding more API keys to reach 100k+ capacity (current: {total_capacity})")
            if result['status'] == 'PASS':
                result['status'] = 'WARN'

        # Test rate limiting
        try:
            from src.data_pipeline.utils.multi_api_key_manager import MultiAPIKeyManager
            key_manager = MultiAPIKeyManager()

            # Test key rotation
            test_source = 'eodhd'
            key1 = key_manager.get_next_key(test_source)
            key2 = key_manager.get_next_key(test_source)

            result['details']['rate_limiting'] = {
                'key_rotation_working': key1 is not None,
                'multiple_keys_available': key1 != key2 if key2 else False
            }

        except Exception as e:
            result['issues'].append(f"Rate limiting test failed: {str(e)}")
            result['status'] = 'FAIL'

        return result

    def _generate_final_assessment(self):
        """Generate final GO/NO-GO assessment"""
        total_sections = len(self.audit_results['sections'])
        passed_sections = len([s for s in self.audit_results['sections'].values() if s['status'] == 'PASS'])
        warning_sections = len([s for s in self.audit_results['sections'].values() if s['status'] == 'WARN'])
        failed_sections = len([s for s in self.audit_results['sections'].values() if s['status'] == 'FAIL'])

        # Collect all issues
        all_issues = []
        all_recommendations = []

        for section_name, section_result in self.audit_results['sections'].items():
            all_issues.extend(section_result.get('issues', []))
            all_recommendations.extend(section_result.get('recommendations', []))

        self.audit_results['issues_found'] = all_issues
        self.audit_results['recommendations'] = all_recommendations

        # Determine overall status
        if failed_sections > 0:
            self.audit_results['overall_status'] = 'FAIL'
            self.audit_results['go_no_go'] = 'NO-GO'
        elif warning_sections > 2:  # More than 2 warnings = caution
            self.audit_results['overall_status'] = 'WARN'
            self.audit_results['go_no_go'] = 'CAUTION'
        else:
            self.audit_results['overall_status'] = 'PASS'
            self.audit_results['go_no_go'] = 'GO'

        # Generate summary
        self.audit_results['summary'] = {
            'total_sections': total_sections,
            'passed': passed_sections,
            'warnings': warning_sections,
            'failed': failed_sections,
            'total_issues': len(all_issues),
            'total_recommendations': len(all_recommendations)
        }

    def _save_audit_report(self):
        """Save comprehensive audit report"""
        report_file = self.project_root / f"pre_production_audit_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(report_file, 'w') as f:
                json.dump(self.audit_results, f, indent=2, default=str)

            self.logger.info(f"📄 Audit report saved: {report_file}")

        except Exception as e:
            self.logger.error(f"❌ Failed to save audit report: {str(e)}")

    def print_audit_summary(self):
        """Print comprehensive audit summary"""
        print("\n" + "="*80)
        print("🔍 PRE-PRODUCTION AUDIT SUMMARY")
        print("="*80)

        summary = self.audit_results['summary']
        print(f"📊 Sections Audited: {summary['total_sections']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"⚠️ Warnings: {summary['warnings']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"🔧 Issues Found: {summary['total_issues']}")
        print(f"💡 Recommendations: {summary['total_recommendations']}")

        print(f"\n🎯 OVERALL STATUS: {self.audit_results['overall_status']}")
        print(f"🚦 GO/NO-GO DECISION: {self.audit_results['go_no_go']}")

        if self.audit_results['issues_found']:
            print(f"\n❌ ISSUES FOUND:")
            for i, issue in enumerate(self.audit_results['issues_found'][:10], 1):
                print(f"   {i}. {issue}")
            if len(self.audit_results['issues_found']) > 10:
                print(f"   ... and {len(self.audit_results['issues_found']) - 10} more")

        if self.audit_results['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(self.audit_results['recommendations'][:5], 1):
                print(f"   {i}. {rec}")
            if len(self.audit_results['recommendations']) > 5:
                print(f"   ... and {len(self.audit_results['recommendations']) - 5} more")

        print("\n" + "="*80)

        # Final decision
        if self.audit_results['go_no_go'] == 'GO':
            print("🚀 RECOMMENDATION: PROCEED WITH 8-HOUR PRODUCTION SESSION")
        elif self.audit_results['go_no_go'] == 'CAUTION':
            print("⚠️ RECOMMENDATION: PROCEED WITH CAUTION - ADDRESS WARNINGS FIRST")
        else:
            print("🛑 RECOMMENDATION: DO NOT PROCEED - RESOLVE CRITICAL ISSUES FIRST")

        print("="*80)

def main():
    """Run the pre-production audit"""
    print("🔍 Starting Pre-Production Audit")
    print("This will comprehensively test all systems before the 8-hour production session")
    print()

    auditor = PreProductionAuditor()

    try:
        # Run comprehensive audit
        results = auditor.run_comprehensive_audit()

        # Print summary
        auditor.print_audit_summary()

        return results['go_no_go'] == 'GO'

    except Exception as e:
        print(f"❌ Audit failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
