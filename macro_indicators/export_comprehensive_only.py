#!/usr/bin/env python3
"""
Export Only Comprehensive Analysis-Ready Files

This script exports only the most valuable files for macro economic analysis,
removing clutter and focusing on actionable insights.
"""

import os
import sqlite3
import pandas as pd
import shutil
from datetime import datetime

def clean_and_export_comprehensive():
    """Clean existing exports and create comprehensive analysis files only"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators_clean.db')
    output_dir = os.path.join(os.path.dirname(__file__), 'exports_comprehensive')
    
    # Remove existing export directory and create fresh one
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    print("🧹 CLEANING AND CREATING COMPREHENSIVE EXPORTS")
    print("=" * 60)
    print("🎯 Focus: Analysis-ready files with maximum insights")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # 1. COMPREHENSIVE PIVOT TABLE - All indicators in wide format
    print("1️⃣ Creating comprehensive pivot table...")
    
    pivot_query = """
    SELECT 
        date,
        series_id,
        value
    FROM observations
    WHERE is_null = 0
    ORDER BY date DESC, series_id
    """
    
    pivot_df = pd.read_sql_query(pivot_query, conn)
    
    if not pivot_df.empty:
        pivot_df['date'] = pd.to_datetime(pivot_df['date'])
        pivot_table = pivot_df.pivot(index='date', columns='series_id', values='value')
        pivot_table = pivot_table.sort_index(ascending=False)
        
        # Export comprehensive pivot
        pivot_table.to_csv(os.path.join(output_dir, 'macro_indicators_comprehensive.csv'))
        print(f"   ✅ {len(pivot_table)} dates × {len(pivot_table.columns)} indicators -> macro_indicators_comprehensive.csv")
    
    # 2. KEY INDICATORS ANALYSIS - Most important indicators with metadata
    print("2️⃣ Creating key indicators analysis dataset...")
    
    # Key indicators for comprehensive analysis
    key_indicators = [
        'FEDFUNDS',    # Federal Funds Rate
        'DGS10',       # 10-Year Treasury
        'DGS2',        # 2-Year Treasury
        'T10Y2Y',      # Yield Curve Spread
        'UNRATE',      # Unemployment Rate
        'PAYEMS',      # Nonfarm Payrolls
        'CPIAUCSL',    # Consumer Price Index
        'CPILFESL',    # Core CPI
        'GDP',         # Gross Domestic Product
        'INDPRO',      # Industrial Production
        'HOUST',       # Housing Starts
        'UMCSENT',     # Consumer Sentiment
        'SP500',       # S&P 500
        'VIXCLS',      # VIX
        'DCOILWTICO',  # Oil Prices
        'M2SL',        # Money Supply
    ]
    
    key_indicators_str = "', '".join(key_indicators)
    key_query = f"""
    SELECT 
        o.date,
        o.series_id,
        i.name as indicator_name,
        i.category,
        i.units,
        i.frequency,
        o.value,
        CASE 
            WHEN LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
            THEN o.value - LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date)
            ELSE NULL 
        END as change,
        CASE 
            WHEN LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
                AND LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date) != 0
            THEN ((o.value - LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date)) / 
                  LAG(o.value) OVER (PARTITION BY o.series_id ORDER BY o.date)) * 100
            ELSE NULL 
        END as percent_change
    FROM observations o
    LEFT JOIN indicators i ON o.series_id = i.series_id
    WHERE o.series_id IN ('{key_indicators_str}') AND o.is_null = 0
    ORDER BY o.date DESC, o.series_id
    """
    
    key_df = pd.read_sql_query(key_query, conn)
    
    if not key_df.empty:
        key_df['date'] = pd.to_datetime(key_df['date'])
        key_df.to_csv(os.path.join(output_dir, 'key_indicators_with_changes.csv'), index=False)
        print(f"   ✅ {len(key_df)} observations for {len(key_indicators)} key indicators -> key_indicators_with_changes.csv")
    
    # 3. ECONOMIC CYCLE INDICATORS - Focused on recession/expansion signals
    print("3️⃣ Creating economic cycle indicators...")
    
    cycle_indicators = [
        'T10Y2Y',      # Yield Curve Spread (recession predictor)
        'T10Y3M',      # Alternative yield spread
        'UNRATE',      # Unemployment (lagging indicator)
        'ICSA',        # Initial Claims (leading indicator)
        'CFNAI',       # Chicago Fed Activity Index
        'USSLIND',     # Leading Economic Index
        'PAYEMS',      # Employment (coincident)
        'INDPRO',      # Industrial Production (coincident)
        'HOUST',       # Housing Starts (leading)
        'UMCSENT',     # Consumer Sentiment (leading)
    ]
    
    cycle_indicators_str = "', '".join(cycle_indicators)
    cycle_query = f"""
    SELECT 
        o.date,
        o.series_id,
        i.name as indicator_name,
        i.category,
        o.value,
        -- 12-month change for trend analysis
        CASE 
            WHEN LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
            THEN o.value - LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date)
            ELSE NULL 
        END as change_12m,
        CASE 
            WHEN LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
                AND LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date) != 0
            THEN ((o.value - LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date)) / 
                  LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date)) * 100
            ELSE NULL 
        END as percent_change_12m
    FROM observations o
    LEFT JOIN indicators i ON o.series_id = i.series_id
    WHERE o.series_id IN ('{cycle_indicators_str}') AND o.is_null = 0
    ORDER BY o.date DESC, o.series_id
    """
    
    cycle_df = pd.read_sql_query(cycle_query, conn)
    
    if not cycle_df.empty:
        cycle_df['date'] = pd.to_datetime(cycle_df['date'])
        cycle_df.to_csv(os.path.join(output_dir, 'economic_cycle_indicators.csv'), index=False)
        print(f"   ✅ {len(cycle_df)} observations for economic cycle analysis -> economic_cycle_indicators.csv")
    
    # 4. INFLATION ANALYSIS - All inflation measures with expectations
    print("4️⃣ Creating inflation analysis dataset...")
    
    inflation_indicators = [
        'CPIAUCSL',    # Headline CPI
        'CPILFESL',    # Core CPI
        'PCEPI',       # PCE Price Index
        'PCEPILFE',    # Core PCE
        'T5YIE',       # 5-Year Breakeven
        'T10YIE',      # 10-Year Breakeven
        'MICH',        # Michigan Expectations
        'PPIACO',      # Producer Prices
    ]
    
    inflation_indicators_str = "', '".join(inflation_indicators)
    inflation_query = f"""
    SELECT 
        o.date,
        o.series_id,
        i.name as indicator_name,
        i.units,
        o.value,
        -- Year-over-year inflation rate
        CASE 
            WHEN LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
                AND LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date) != 0
                AND o.series_id IN ('CPIAUCSL', 'CPILFESL', 'PCEPI', 'PCEPILFE', 'PPIACO')
            THEN ((o.value - LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date)) / 
                  LAG(o.value, 12) OVER (PARTITION BY o.series_id ORDER BY o.date)) * 100
            ELSE o.value  -- For rates/expectations, use actual value
        END as inflation_rate
    FROM observations o
    LEFT JOIN indicators i ON o.series_id = i.series_id
    WHERE o.series_id IN ('{inflation_indicators_str}') AND o.is_null = 0
    ORDER BY o.date DESC, o.series_id
    """
    
    inflation_df = pd.read_sql_query(inflation_query, conn)
    
    if not inflation_df.empty:
        inflation_df['date'] = pd.to_datetime(inflation_df['date'])
        inflation_df.to_csv(os.path.join(output_dir, 'inflation_comprehensive.csv'), index=False)
        print(f"   ✅ {len(inflation_df)} observations for inflation analysis -> inflation_comprehensive.csv")
    
    # 5. MARKET INDICATORS - Financial market data with volatility
    print("5️⃣ Creating market indicators dataset...")
    
    market_indicators = [
        'SP500',       # S&P 500
        'VIXCLS',      # VIX
        'DGS10',       # 10-Year Treasury
        'FEDFUNDS',    # Fed Funds Rate
        'DCOILWTICO',  # Oil Prices
        'DHHNGSP',     # Natural Gas
        'BAMLH0A0HYM2', # High Yield Spread
        'BAMLC0A0CM',  # Investment Grade Spread
    ]
    
    market_indicators_str = "', '".join([ind for ind in market_indicators if ind in key_indicators + ['DHHNGSP', 'BAMLH0A0HYM2', 'BAMLC0A0CM']])
    market_query = f"""
    SELECT 
        o.date,
        o.series_id,
        i.name as indicator_name,
        i.units,
        o.value,
        -- 1-month change
        CASE 
            WHEN LAG(o.value, 22) OVER (PARTITION BY o.series_id ORDER BY o.date) IS NOT NULL 
                AND LAG(o.value, 22) OVER (PARTITION BY o.series_id ORDER BY o.date) != 0
            THEN ((o.value - LAG(o.value, 22) OVER (PARTITION BY o.series_id ORDER BY o.date)) / 
                  LAG(o.value, 22) OVER (PARTITION BY o.series_id ORDER BY o.date)) * 100
            ELSE NULL 
        END as change_1m_pct
    FROM observations o
    LEFT JOIN indicators i ON o.series_id = i.series_id
    WHERE o.series_id IN ('{market_indicators_str}') AND o.is_null = 0
    ORDER BY o.date DESC, o.series_id
    """
    
    market_df = pd.read_sql_query(market_query, conn)
    
    if not market_df.empty:
        market_df['date'] = pd.to_datetime(market_df['date'])
        market_df.to_csv(os.path.join(output_dir, 'market_indicators.csv'), index=False)
        print(f"   ✅ {len(market_df)} observations for market analysis -> market_indicators.csv")
    
    # 6. METADATA SUMMARY - Complete indicator reference
    print("6️⃣ Creating metadata summary...")
    
    metadata_query = """
    SELECT 
        series_id,
        name,
        category,
        units,
        frequency,
        description,
        first_date,
        last_date,
        (SELECT COUNT(*) FROM observations WHERE series_id = i.series_id) as total_observations,
        (SELECT COUNT(*) FROM observations WHERE series_id = i.series_id AND is_null = 0) as valid_observations,
        ROUND(
            (SELECT COUNT(*) FROM observations WHERE series_id = i.series_id AND is_null = 0) * 100.0 / 
            (SELECT COUNT(*) FROM observations WHERE series_id = i.series_id), 2
        ) as completeness_pct
    FROM indicators i
    ORDER BY category, name
    """
    
    metadata_df = pd.read_sql_query(metadata_query, conn)
    metadata_df.to_csv(os.path.join(output_dir, 'indicators_reference.csv'), index=False)
    print(f"   ✅ {len(metadata_df)} indicators reference -> indicators_reference.csv")
    
    conn.close()
    
    # Calculate total export size
    total_size = 0
    file_count = 0
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('.csv'):
                filepath = os.path.join(root, file)
                size = os.path.getsize(filepath)
                total_size += size
                file_count += 1
    
    print("\n" + "=" * 60)
    print("✅ COMPREHENSIVE EXPORTS COMPLETED!")
    print("=" * 60)
    print(f"📁 Export directory: {output_dir}")
    print(f"📊 Files exported: {file_count} (streamlined for analysis)")
    print(f"💾 Total size: {total_size / (1024*1024):.2f} MB")
    
    print(f"\n📋 EXPORTED FILES:")
    print(f"  1. macro_indicators_comprehensive.csv - Complete pivot table")
    print(f"  2. key_indicators_with_changes.csv - 16 key indicators with changes")
    print(f"  3. economic_cycle_indicators.csv - Recession/expansion signals")
    print(f"  4. inflation_comprehensive.csv - All inflation measures")
    print(f"  5. market_indicators.csv - Financial market data")
    print(f"  6. indicators_reference.csv - Complete metadata reference")
    
    print(f"\n🎯 READY FOR SOPHISTICATED ANALYSIS!")
    print(f"📊 Focus areas: Economic cycles, inflation, market dynamics")
    print(f"🔍 All files optimized for correlation, trend, and predictive analysis")
    
    return output_dir

if __name__ == "__main__":
    export_dir = clean_and_export_comprehensive()
    print(f"\n📁 Comprehensive exports available at: {export_dir}")
