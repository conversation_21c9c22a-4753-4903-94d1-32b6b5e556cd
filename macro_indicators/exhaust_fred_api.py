#!/usr/bin/env python3
"""
EXHAUST FRED API - Collect EVERYTHING Available

This script systematically collects ALL available macro economic indicators
from FRED API, handling rate limits and maximizing data collection.
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
import time
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.collectors.fred_collector import FREDCollector

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# COMPREHENSIVE INDICATOR REGISTRY - EVERYTHING WE CAN GET
EXHAUSTIVE_INDICATORS = {
    
    # ========== INTEREST RATES & MONETARY POLICY ==========
    'interest_rates': {
        # Federal Funds
        'FEDFUNDS': 'Federal Funds Rate',
        'DFEDTARU': 'Federal Funds Upper Target',
        'DFEDTARL': 'Federal Funds Lower Target',
        'EFFR': 'Effective Federal Funds Rate',
        
        # Treasury Rates
        'DGS1MO': '1-Month Treasury Rate',
        'DGS3MO': '3-Month Treasury Rate',
        'DGS6MO': '6-Month Treasury Rate',
        'DGS1': '1-Year Treasury Rate',
        'DGS2': '2-Year Treasury Rate',
        'DGS3': '3-Year Treasury Rate',
        'DGS5': '5-Year Treasury Rate',
        'DGS7': '7-Year Treasury Rate',
        'DGS10': '10-Year Treasury Rate',
        'DGS20': '20-Year Treasury Rate',
        'DGS30': '30-Year Treasury Rate',
        
        # Yield Spreads
        'T10Y2Y': 'Yield Curve Spread (10Y-2Y)',
        'T10Y3M': 'Yield Curve Spread (10Y-3M)',
        'T5Y5Y': '5-Year Forward Inflation Expectation Rate',
        
        # Corporate Bonds
        'AAA': 'AAA Corporate Bond Yield',
        'BAA': 'BAA Corporate Bond Yield',
        'BAMLH0A0HYM2': 'High Yield Credit Spread',
        'BAMLC0A0CM': 'Investment Grade Credit Spread',
        'BAMLC0A1CAAATRIV': 'AAA Credit Spread',
        'BAMLC0A2CAATRIV': 'AA Credit Spread',
        'BAMLC0A3CATRIV': 'A Credit Spread',
        'BAMLC0A4CBBBTRIV': 'BBB Credit Spread',
        
        # Mortgage Rates
        'MORTGAGE30US': '30-Year Fixed Mortgage Rate',
        'MORTGAGE15US': '15-Year Fixed Mortgage Rate',
        'MORTGAGE5US': '5/1-Year Adjustable Rate Mortgage',
        
        # Other Rates
        'DPRIME': 'Bank Prime Loan Rate',
        'MPRIME': 'Bank Prime Loan Rate (Monthly)',
        'TEDRATE': 'TED Spread',
        'TB3MS': '3-Month Treasury Bill Rate',
        'TB6MS': '6-Month Treasury Bill Rate',
        'GS1': '1-Year Treasury Rate (Monthly)',
        'GS2': '2-Year Treasury Rate (Monthly)',
        'GS5': '5-Year Treasury Rate (Monthly)',
        'GS10': '10-Year Treasury Rate (Monthly)',
        'GS30': '30-Year Treasury Rate (Monthly)',
    },
    
    # ========== ECONOMIC GROWTH & ACTIVITY ==========
    'economic_growth': {
        # GDP
        'GDP': 'Gross Domestic Product',
        'GDPC1': 'Real GDP',
        'GDPPOT': 'Real Potential GDP',
        'GDPCA': 'Real GDP per Capita',
        'A191RL1Q225SBEA': 'GDP Growth Rate (QoQ)',
        'A191RL1A225NBEA': 'GDP Growth Rate (YoY)',
        'NYGDPMKTPCDWLD': 'GDP per Capita (World Bank)',
        
        # Industrial Production
        'INDPRO': 'Industrial Production Index',
        'IPMAN': 'Manufacturing Production',
        'IPMINE': 'Mining Production',
        'IPUTL': 'Utilities Production',
        'IPFUELS': 'Fuels Production',
        'IPDMAT': 'Durable Materials Production',
        'IPNMAT': 'Nondurable Materials Production',
        'IPDCONGD': 'Durable Consumer Goods Production',
        'IPNCONGD': 'Nondurable Consumer Goods Production',
        'IPBUSEQ': 'Business Equipment Production',
        
        # Capacity Utilization
        'TCU': 'Total Capacity Utilization',
        'MCUMFN': 'Manufacturing Capacity Utilization',
        'CUMFNS': 'Manufacturing Capacity Utilization (SA)',
        
        # Business Activity Indices
        'CFNAI': 'Chicago Fed National Activity Index',
        'CFNAIMA3': 'Chicago Fed National Activity Index (3-Month MA)',
        'CFNAIDIFF': 'Chicago Fed National Activity Index (Diffusion)',
        'USSLIND': 'Leading Economic Index',
        'USALOLITONOSTSAM': 'Coincident Economic Index',
        'USPHCI': 'Philadelphia Fed Coincident Index',
        
        # Productivity
        'OPHNFB': 'Nonfarm Business Productivity',
        'OPHPBS': 'Private Business Productivity',
        'ULCNFB': 'Unit Labor Costs (Nonfarm Business)',
        'ULCBS': 'Unit Labor Costs (Business Sector)',
        
        # PMI and Surveys
        'NAPM': 'ISM Manufacturing PMI',
        'NAPMNOI': 'ISM Manufacturing New Orders',
        'NAPMPI': 'ISM Manufacturing Prices Paid',
        'NAPMEI': 'ISM Manufacturing Employment',
        'NAPMSII': 'ISM Services PMI',
        'NAPMNOI': 'ISM Services New Orders',
        'NAPMII': 'ISM Services Inventories',
    },
    
    # ========== EMPLOYMENT & LABOR MARKET ==========
    'employment': {
        # Unemployment
        'UNRATE': 'Unemployment Rate',
        'UNRATENSA': 'Unemployment Rate (NSA)',
        'U1RATE': 'U-1 Unemployment Rate',
        'U2RATE': 'U-2 Unemployment Rate',
        'U3RATE': 'U-3 Unemployment Rate',
        'U4RATE': 'U-4 Unemployment Rate',
        'U5RATE': 'U-5 Unemployment Rate',
        'U6RATE': 'U-6 Unemployment Rate',
        'NROU': 'Natural Rate of Unemployment',
        'NROUST': 'Natural Rate of Unemployment (Short-term)',
        'NROUL': 'Natural Rate of Unemployment (Long-term)',
        
        # Employment
        'PAYEMS': 'Total Nonfarm Payrolls',
        'USPRIV': 'Private Nonfarm Payrolls',
        'USGOVT': 'Government Employment',
        'USFIRE': 'Financial Activities Employment',
        'USPBS': 'Professional & Business Services Employment',
        'USEHS': 'Education & Health Services Employment',
        'USLAH': 'Leisure & Hospitality Employment',
        'USWTRADE': 'Wholesale Trade Employment',
        'USTRADE': 'Retail Trade Employment',
        'USTPU': 'Transportation & Utilities Employment',
        'USINFO': 'Information Employment',
        'MANEMP': 'Manufacturing Employment',
        'USCONS': 'Construction Employment',
        'USMINE': 'Mining & Logging Employment',
        
        # Labor Force
        'CLF16OV': 'Civilian Labor Force',
        'CIVPART': 'Labor Force Participation Rate',
        'EMRATIO': 'Employment-Population Ratio',
        'LNS11300060': 'Labor Force Participation Rate (25-54)',
        'LNS12300060': 'Employment-Population Ratio (25-54)',
        
        # Jobless Claims
        'ICSA': 'Initial Jobless Claims',
        'CCSA': 'Continuing Jobless Claims',
        'IC4WSA': '4-Week Moving Average Initial Claims',
        'CCNSA': 'Continuing Claims (NSA)',
        
        # Job Openings and Labor Turnover
        'JTSJOL': 'Job Openings',
        'JTSQUR': 'Quits Rate',
        'JTSHIR': 'Hires Rate',
        'JTSLDL': 'Layoffs and Discharges Rate',
        'JTSSEP': 'Separations Rate',
        'JTSTSL': 'Total Separations Level',
        
        # Wages and Hours
        'AHETPI': 'Average Hourly Earnings (Total Private)',
        'AHEMAN': 'Average Hourly Earnings (Manufacturing)',
        'AWHAETP': 'Average Weekly Hours (Total Private)',
        'AWHAEMAN': 'Average Weekly Hours (Manufacturing)',
        'AWHNONAG': 'Average Weekly Hours (Nonfarm)',
        'CES0500000003': 'Average Hourly Earnings (Private)',
        'CES0500000008': 'Average Weekly Hours (Private)',
        
        # Employment Cost Index
        'ECIALLCIV': 'Employment Cost Index (All Civilian)',
        'ECIWAG': 'Employment Cost Index (Wages)',
        'ECIBENF': 'Employment Cost Index (Benefits)',
    },
    
    # ========== INFLATION & PRICES ==========
    'inflation': {
        # Consumer Price Index
        'CPIAUCSL': 'Consumer Price Index',
        'CPILFESL': 'Core CPI (Less Food & Energy)',
        'CPIUFDSL': 'CPI Food',
        'CPIENGSL': 'CPI Energy',
        'CPIHOSSL': 'CPI Housing',
        'CPITRNSL': 'CPI Transportation',
        'CPIMEDSL': 'CPI Medical Care',
        'CPIRECSL': 'CPI Recreation',
        'CPIEDUSL': 'CPI Education',
        'CPIOGSSL': 'CPI Other Goods & Services',
        'CPIAUCNS': 'CPI (NSA)',
        'CPILFESL': 'Core CPI (NSA)',
        
        # Producer Price Index
        'PPIACO': 'Producer Price Index',
        'PPILFE': 'Core PPI (Less Food & Energy)',
        'PPIENG': 'PPI Energy',
        'PPIFOOD': 'PPI Food',
        'PPIFGS': 'PPI Finished Goods',
        'PPIIDC': 'PPI Intermediate Demand Commodities',
        'PPIITM': 'PPI Intermediate Materials',
        'PPICMM': 'PPI Commodities',
        
        # PCE Price Index
        'PCEPI': 'PCE Price Index',
        'PCEPILFE': 'Core PCE Price Index',
        'PCEDG': 'PCE Durable Goods',
        'PCEND': 'PCE Nondurable Goods',
        'PCES': 'PCE Services',
        'PCEPILFE': 'Core PCE (Less Food & Energy)',
        
        # GDP Deflator
        'GDPDEF': 'GDP Deflator',
        'GDPCTPI': 'GDP Chain-type Price Index',
        
        # Import/Export Prices
        'IR': 'Import Price Index',
        'IQ': 'Export Price Index',
        'IRTOT': 'Import Price Index (Total)',
        'IQTOT': 'Export Price Index (Total)',
        
        # Breakeven Inflation Rates
        'T5YIE': '5-Year Breakeven Inflation Rate',
        'T10YIE': '10-Year Breakeven Inflation Rate',
        'T5YIFR': '5-Year Forward Inflation Expectation Rate',
        
        # Survey-based Inflation Expectations
        'MICH': 'University of Michigan Inflation Expectations',
        'EXPINF1YR': '1-Year Inflation Expectations',
        'EXPINF5TO10': '5-10 Year Inflation Expectations',
        
        # Real Interest Rates
        'REAINTRATREARAT10Y': 'Real Interest Rate (10-Year)',
        'DFII10': '10-Year TIPS Rate',
        'DFII5': '5-Year TIPS Rate',
    },
}

def collect_with_rate_limit_handling(collector: FREDCollector, series_id: str, name: str, 
                                   category: str, conn: sqlite3.Connection, 
                                   start_date: str = '2000-01-01', 
                                   max_retries: int = 3) -> dict:
    """
    Collect indicator with intelligent rate limit handling
    """
    
    stats = {
        'series_id': series_id,
        'name': name,
        'category': category,
        'success': False,
        'observations': 0,
        'null_values': 0,
        'error': None,
        'retry_count': 0
    }
    
    for attempt in range(max_retries):
        try:
            # Add delay between requests to avoid rate limits
            if attempt > 0:
                delay = min(2 ** attempt, 10)  # Exponential backoff, max 10 seconds
                logger.info(f"    🔄 Retry {attempt + 1}/{max_retries} after {delay}s delay...")
                time.sleep(delay)
            
            # Get series metadata
            metadata = collector.get_series_info(series_id)
            
            if not metadata:
                stats['error'] = 'No metadata found'
                continue
            
            # Get series data
            data = collector.get_series_data(series_id, start_date=start_date)
            
            if data.empty:
                stats['error'] = 'No data returned'
                continue
            
            # Clean and validate data
            data['is_null'] = data['value'].isna().astype(int)
            data['value'] = pd.to_numeric(data['value'], errors='coerce')
            data['is_null'] = data['value'].isna().astype(int)
            
            cursor = conn.cursor()
            
            # Save indicator metadata
            cursor.execute('''
            INSERT OR REPLACE INTO indicators 
            (series_id, name, category, units, frequency, description, first_date, last_date, last_updated, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                series_id,
                name,
                category,
                metadata.get('units', ''),
                metadata.get('frequency', ''),
                metadata.get('notes', ''),
                data['date'].min().strftime('%Y-%m-%d') if not data.empty else None,
                data['date'].max().strftime('%Y-%m-%d') if not data.empty else None,
                datetime.now().isoformat(),
                json.dumps(metadata)
            ))
            
            # Save observations
            saved_count = 0
            null_count = 0
            
            for _, row in data.iterrows():
                try:
                    is_null = pd.isna(row['value'])
                    cursor.execute('''
                    INSERT OR REPLACE INTO observations
                    (series_id, date, value, is_null)
                    VALUES (?, ?, ?, ?)
                    ''', (
                        series_id,
                        row['date'].strftime('%Y-%m-%d'),
                        row['value'] if not is_null else None,
                        int(is_null)
                    ))
                    saved_count += 1
                    if is_null:
                        null_count += 1
                except Exception as e:
                    logger.warning(f"    ⚠️ Error saving observation: {e}")
            
            conn.commit()
            
            # Success!
            stats['success'] = True
            stats['observations'] = saved_count
            stats['null_values'] = null_count
            stats['retry_count'] = attempt
            
            logger.info(f"    ✅ {saved_count} observations ({null_count} nulls)")
            break
            
        except Exception as e:
            error_msg = str(e)
            stats['error'] = error_msg
            stats['retry_count'] = attempt + 1
            
            # Check if it's a rate limit error
            if '429' in error_msg or 'Too Many Requests' in error_msg:
                if attempt < max_retries - 1:
                    delay = min(5 * (2 ** attempt), 30)  # Longer delay for rate limits
                    logger.warning(f"    ⚠️ Rate limited, waiting {delay}s before retry...")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"    ❌ Rate limit exceeded after {max_retries} attempts")
            else:
                logger.error(f"    ❌ Error: {error_msg}")
                break
    
    return stats

def exhaust_fred_api(db_path: str, start_date: str = '2000-01-01'):
    """
    Systematically exhaust the FRED API to collect all available data
    """
    
    # Initialize collector
    collector = FREDCollector()
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # Overall statistics
    overall_stats = {
        'start_time': datetime.now(),
        'categories': [],
        'total_indicators': 0,
        'successful_indicators': 0,
        'failed_indicators': 0,
        'total_observations': 0,
        'rate_limited_count': 0
    }
    
    logger.info(f"🚀 EXHAUSTING FRED API - COLLECTING EVERYTHING!")
    logger.info(f"📊 Target: {sum(len(indicators) for indicators in EXHAUSTIVE_INDICATORS.values())} indicators")
    logger.info(f"📅 Start date: {start_date}")
    logger.info(f"⏱️ Estimated time: ~30-60 minutes with rate limiting")
    
    # Collect data by category
    for category_name, indicators in EXHAUSTIVE_INDICATORS.items():
        logger.info(f"\n🔄 CATEGORY: {category_name.upper()} ({len(indicators)} indicators)")
        
        category_stats = {
            'category': category_name,
            'total': len(indicators),
            'successful': 0,
            'failed': 0,
            'rate_limited': 0,
            'total_observations': 0,
            'indicators': []
        }
        
        for i, (series_id, name) in enumerate(indicators.items(), 1):
            logger.info(f"  [{i}/{len(indicators)}] {name} ({series_id})...")
            
            # Collect with rate limit handling
            stats = collect_with_rate_limit_handling(
                collector, series_id, name, category_name, conn, start_date
            )
            
            category_stats['indicators'].append(stats)
            
            if stats['success']:
                category_stats['successful'] += 1
                category_stats['total_observations'] += stats['observations']
            else:
                category_stats['failed'] += 1
                if '429' in str(stats['error']) or 'Too Many Requests' in str(stats['error']):
                    category_stats['rate_limited'] += 1
            
            # Add small delay between requests
            time.sleep(0.1)
        
        overall_stats['categories'].append(category_stats)
        overall_stats['total_indicators'] += category_stats['total']
        overall_stats['successful_indicators'] += category_stats['successful']
        overall_stats['failed_indicators'] += category_stats['failed']
        overall_stats['total_observations'] += category_stats['total_observations']
        overall_stats['rate_limited_count'] += category_stats['rate_limited']
        
        success_rate = (category_stats['successful'] / category_stats['total'] * 100) if category_stats['total'] > 0 else 0
        logger.info(f"✅ {category_name}: {category_stats['successful']}/{category_stats['total']} ({success_rate:.1f}%) - {category_stats['total_observations']:,} obs")
        
        # Longer pause between categories to avoid rate limits
        if category_name != list(EXHAUSTIVE_INDICATORS.keys())[-1]:  # Not the last category
            logger.info("    ⏸️ Pausing 5 seconds between categories...")
            time.sleep(5)
    
    overall_stats['end_time'] = datetime.now()
    overall_stats['duration'] = (overall_stats['end_time'] - overall_stats['start_time']).total_seconds()
    
    conn.close()
    
    return overall_stats

def print_exhaustive_summary(stats: dict):
    """Print comprehensive summary of exhaustive collection"""
    
    print("\n" + "=" * 100)
    print("🎯 FRED API EXHAUSTIVE COLLECTION SUMMARY")
    print("=" * 100)
    
    # Overall statistics
    success_rate = (stats['successful_indicators'] / stats['total_indicators'] * 100) if stats['total_indicators'] > 0 else 0
    
    print(f"🏆 FINAL RESULTS:")
    print(f"  📊 Total indicators attempted: {stats['total_indicators']}")
    print(f"  ✅ Successfully collected: {stats['successful_indicators']}")
    print(f"  ❌ Failed: {stats['failed_indicators']}")
    print(f"  🚫 Rate limited: {stats['rate_limited_count']}")
    print(f"  📈 Overall success rate: {success_rate:.1f}%")
    print(f"  📋 Total observations: {stats['total_observations']:,}")
    print(f"  ⏱️ Total duration: {stats['duration']/60:.1f} minutes")
    print(f"  ⚡ Average per indicator: {stats['duration']/stats['total_indicators']:.1f} seconds")
    
    # Category breakdown
    print(f"\n📊 CATEGORY BREAKDOWN:")
    for cat_stats in stats['categories']:
        cat_success_rate = (cat_stats['successful'] / cat_stats['total'] * 100) if cat_stats['total'] > 0 else 0
        print(f"  📈 {cat_stats['category']}: {cat_stats['successful']}/{cat_stats['total']} ({cat_success_rate:.1f}%) - {cat_stats['total_observations']:,} obs")
    
    # Top categories by data volume
    print(f"\n🏆 TOP CATEGORIES BY DATA VOLUME:")
    sorted_categories = sorted(stats['categories'], key=lambda x: x['total_observations'], reverse=True)
    for i, cat_stats in enumerate(sorted_categories[:5], 1):
        print(f"  {i}. {cat_stats['category']}: {cat_stats['total_observations']:,} observations")

def main():
    """Main exhaustive collection function"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators_exhaustive.db')
    
    print("🚀 FRED API EXHAUSTIVE DATA COLLECTION")
    print("=" * 100)
    print("🎯 MISSION: Collect EVERYTHING available from FRED API")
    print("📊 Target: 200+ comprehensive macro economic indicators")
    print("📅 Historical coverage: 2000-2025 (25+ years)")
    print("⚠️ Note: This will take 30-60 minutes due to rate limiting")
    print("=" * 100)
    
    # Create fresh database for exhaustive collection
    if os.path.exists(db_path):
        backup_path = db_path.replace('.db', f'_backup_{int(time.time())}.db')
        os.rename(db_path, backup_path)
        logger.info(f"📦 Backed up existing database to: {backup_path}")
    
    # Create clean database
    from fix_data_quality import create_clean_database
    create_clean_database(db_path)
    
    # Start exhaustive collection
    stats = exhaust_fred_api(db_path, start_date='2000-01-01')
    
    # Print comprehensive summary
    print_exhaustive_summary(stats)
    
    print(f"\n✅ EXHAUSTIVE COLLECTION COMPLETED!")
    print(f"📊 Database: {db_path}")
    print(f"🎯 Ready for comprehensive macro analysis!")
    print(f"📈 Next: Run export script to generate CSV files")

if __name__ == "__main__":
    main()
