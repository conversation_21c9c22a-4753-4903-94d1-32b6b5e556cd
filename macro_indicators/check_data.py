#!/usr/bin/env python3
"""
Check collected macro indicator data
"""

import sqlite3
import pandas as pd
import os

def check_database():
    """Check what data we have in the database"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators.db')
    
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return
    
    print(f"📊 Checking database: {db_path}")
    print("=" * 50)
    
    conn = sqlite3.connect(db_path)
    
    # Check tables
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print(f"📋 Tables: {[table[0] for table in tables]}")
    
    # Check observations table
    if ('observations',) in tables:
        print("\n📊 OBSERVATIONS TABLE:")
        
        # Get total count
        cursor.execute("SELECT COUNT(*) FROM observations")
        total_obs = cursor.fetchone()[0]
        print(f"  Total observations: {total_obs}")
        
        # Get count by indicator
        cursor.execute("""
        SELECT indicator_id, COUNT(*) as count 
        FROM observations 
        GROUP BY indicator_id 
        ORDER BY count DESC
        """)
        
        indicator_counts = cursor.fetchall()
        print(f"  Indicators with data: {len(indicator_counts)}")
        
        for indicator_id, count in indicator_counts:
            print(f"    • {indicator_id}: {count} observations")
        
        # Get sample data
        print(f"\n📈 SAMPLE DATA:")
        cursor.execute("""
        SELECT indicator_id, date, value 
        FROM observations 
        ORDER BY date DESC 
        LIMIT 10
        """)
        
        sample_data = cursor.fetchall()
        for indicator_id, date, value in sample_data:
            print(f"  {date}: {indicator_id} = {value}")
    
    # Check indicators table
    if ('indicators',) in tables:
        print("\n📋 INDICATORS TABLE:")
        
        cursor.execute("SELECT COUNT(*) FROM indicators")
        total_indicators = cursor.fetchone()[0]
        print(f"  Total indicators: {total_indicators}")
        
        # Get indicator info
        cursor.execute("SELECT * FROM indicators LIMIT 5")
        columns = [description[0] for description in cursor.description]
        print(f"  Columns: {columns}")
    
    conn.close()
    
    # Database size
    db_size = os.path.getsize(db_path)
    print(f"\n💾 Database size: {db_size / (1024*1024):.2f} MB")

if __name__ == "__main__":
    check_database()
