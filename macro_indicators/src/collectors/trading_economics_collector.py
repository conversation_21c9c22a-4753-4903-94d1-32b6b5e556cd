"""
Trading Economics Data Collector

This module handles data collection from the Trading Economics API.
"""

import os
import logging
import pandas as pd
import tradingeconomics as te
from datetime import datetime
from typing import Dict, List, Optional, Union

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingEconomicsCollector:
    """
    Collector for Trading Economics data.
    
    This class handles authentication and data collection from the Trading Economics API.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Trading Economics collector.
        
        Args:
            api_key: Trading Economics API key. If None, will try to get from environment.
        """
        self.api_key = api_key or os.getenv('TRADING_ECONOMICS')
        if not self.api_key:
            raise ValueError("Trading Economics API key not provided and not found in environment variables")
        
        # Authenticate with Trading Economics
        te.login(self.api_key)
        logger.info("Initialized Trading Economics collector")
    
    def get_indicators(self, country: str = 'united states', output_type: str = 'df') -> pd.DataFrame:
        """
        Get all available indicators for a specific country.
        
        Args:
            country: Country name (default: 'united states')
            output_type: Output format ('df' for DataFrame, 'dict' for dictionary)
            
        Returns:
            DataFrame or dict with indicator data
        """
        try:
            logger.info(f"Fetching indicators for {country}")
            data = te.getIndicatorData(country=country, output_type=output_type)
            logger.info(f"Successfully fetched {len(data)} indicators for {country}")
            return data
        except Exception as e:
            logger.error(f"Error fetching indicators for {country}: {str(e)}")
            raise
    
    def get_indicator_historical(self, 
                               country: str, 
                               indicator: str,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None,
                               output_type: str = 'df') -> pd.DataFrame:
        """
        Get historical data for a specific indicator.
        
        Args:
            country: Country name
            indicator: Indicator name
            start_date: Start date in format 'YYYY-MM-DD'
            end_date: End date in format 'YYYY-MM-DD'
            output_type: Output format ('df' for DataFrame, 'dict' for dictionary)
            
        Returns:
            DataFrame or dict with historical indicator data
        """
        try:
            logger.info(f"Fetching historical data for {indicator} in {country}")
            data = te.getHistoricalData(
                country=country,
                indicator=indicator,
                initDate=start_date,
                endDate=end_date,
                output_type=output_type
            )
            logger.info(f"Successfully fetched historical data for {indicator} in {country}")
            return data
        except Exception as e:
            logger.error(f"Error fetching historical data for {indicator} in {country}: {str(e)}")
            raise
    
    def get_calendar(self, 
                   country: Optional[str] = None,
                   category: Optional[str] = None,
                   output_type: str = 'df') -> pd.DataFrame:
        """
        Get economic calendar data.
        
        Args:
            country: Country name (optional)
            category: Category name (optional)
            output_type: Output format ('df' for DataFrame, 'dict' for dictionary)
            
        Returns:
            DataFrame or dict with calendar data
        """
        try:
            logger.info(f"Fetching calendar data for {country or 'all countries'}")
            data = te.getCalendarData(
                country=country,
                category=category,
                output_type=output_type
            )
            logger.info(f"Successfully fetched calendar data")
            return data
        except Exception as e:
            logger.error(f"Error fetching calendar data: {str(e)}")
            raise
    
    def get_markets(self, 
                  category: Optional[str] = None,
                  output_type: str = 'df') -> pd.DataFrame:
        """
        Get market data.
        
        Args:
            category: Market category (e.g., 'commodities', 'currency', 'index')
            output_type: Output format ('df' for DataFrame, 'dict' for dictionary)
            
        Returns:
            DataFrame or dict with market data
        """
        try:
            logger.info(f"Fetching market data for category: {category or 'all categories'}")
            data = te.getMarketsData(
                category=category,
                output_type=output_type
            )
            logger.info(f"Successfully fetched market data")
            return data
        except Exception as e:
            logger.error(f"Error fetching market data: {str(e)}")
            raise
    
    def get_indicator_by_category(self, 
                                category: str,
                                countries: Optional[List[str]] = None,
                                output_type: str = 'df') -> pd.DataFrame:
        """
        Get indicators by category.
        
        Args:
            category: Indicator category
            countries: List of countries (optional)
            output_type: Output format ('df' for DataFrame, 'dict' for dictionary)
            
        Returns:
            DataFrame or dict with indicator data
        """
        if countries is None:
            countries = ['united states']
            
        try:
            logger.info(f"Fetching {category} indicators for {', '.join(countries)}")
            all_data = []
            
            for country in countries:
                data = te.getIndicatorByCategoryGroup(
                    category=category,
                    country=country,
                    output_type=output_type
                )
                if isinstance(data, pd.DataFrame):
                    all_data.append(data)
                else:
                    all_data.extend(data)
            
            if output_type == 'df' and all_data:
                result = pd.concat(all_data, ignore_index=True)
            else:
                result = all_data
                
            logger.info(f"Successfully fetched {category} indicators")
            return result
        except Exception as e:
            logger.error(f"Error fetching {category} indicators: {str(e)}")
            raise
