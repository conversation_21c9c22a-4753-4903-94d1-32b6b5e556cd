#!/usr/bin/env python3
"""
Test script for Trading Economics API
"""

import os
import tradingeconomics as te
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_trading_economics_api():
    """Test the Trading Economics API connection and basic functionality"""
    
    # Get API key from environment
    api_key = os.getenv('TRADING_ECONOMIC')
    
    if not api_key:
        print("❌ TRADING_ECONOMICS API key not found in environment variables")
        return False
    
    print(f"✅ Found Trading Economics API key: {api_key[:10]}...")
    
    try:
        # Login to Trading Economics
        te.login(api_key)
        print("✅ Successfully logged in to Trading Economics")
        
        # Test basic functionality - try with Mexico (often available in free tier)
        print("\n📊 Testing getIndicatorData for Mexico...")
        data = te.getIndicatorData(country='Mexico')
        
        if data:
            print(f"✅ Successfully retrieved {len(data)} indicators for United States")
            
            # Show first few indicators
            print("\n📋 Sample indicators:")
            for i, indicator in enumerate(data[:5]):
                if isinstance(indicator, dict):
                    category = indicator.get('Category', 'N/A')
                    title = indicator.get('Title', 'N/A')
                    latest_value = indicator.get('LatestValue', 'N/A')
                    unit = indicator.get('Unit', 'N/A')
                    print(f"  {i+1}. {title}: {latest_value} {unit} (Category: {category})")
                else:
                    print(f"  {i+1}. {indicator}")
        else:
            print("⚠️ No data returned")
            
        # Test with DataFrame output
        print("\n📊 Testing with DataFrame output...")
        df_data = te.getIndicatorData(country='Mexico', output_type='df')
        
        if df_data is not None and not df_data.empty:
            print(f"✅ Successfully retrieved DataFrame with {len(df_data)} rows")
            print(f"📋 Columns: {list(df_data.columns)}")
            print(f"📊 Sample data:")
            print(df_data.head())
        else:
            print("⚠️ No DataFrame data returned")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing Trading Economics API: {str(e)}")
        return False

def test_specific_indicators():
    """Test getting specific economic indicators"""
    
    try:
        print("\n🔍 Testing specific indicators...")
        
        # Test getting GDP data
        print("📈 Getting GDP data...")
        gdp_data = te.getHistoricalData(country='Mexico', indicator='GDP', output_type='df')

        if gdp_data is not None and not gdp_data.empty:
            print(f"✅ GDP data: {len(gdp_data)} records")
            print(f"📊 Latest GDP data:")
            print(gdp_data.tail())
        else:
            print("⚠️ No GDP data returned")

        # Test getting unemployment rate
        print("\n📊 Getting Unemployment Rate data...")
        unemployment_data = te.getHistoricalData(country='Mexico', indicator='Unemployment Rate', output_type='df')
        
        if unemployment_data is not None and not unemployment_data.empty:
            print(f"✅ Unemployment data: {len(unemployment_data)} records")
            print(f"📊 Latest unemployment data:")
            print(unemployment_data.tail())
        else:
            print("⚠️ No unemployment data returned")
            
    except Exception as e:
        print(f"❌ Error testing specific indicators: {str(e)}")

def test_markets_data():
    """Test getting markets data"""
    
    try:
        print("\n💹 Testing markets data...")
        
        # Get markets data (no category parameter in this version)
        markets_data = te.getMarketsData(output_type='df')
        
        if markets_data is not None and not markets_data.empty:
            print(f"✅ Markets data: {len(markets_data)} records")
            print(f"📊 Sample markets data:")
            print(markets_data.head())
        else:
            print("⚠️ No commodities data returned")
            
    except Exception as e:
        print(f"❌ Error testing markets data: {str(e)}")

if __name__ == "__main__":
    print("🧪 Testing Trading Economics API Connection")
    print("=" * 50)
    
    # Test basic API connection
    if test_trading_economics_api():
        print("\n" + "=" * 50)
        
        # Test specific indicators
        test_specific_indicators()
        
        print("\n" + "=" * 50)
        
        # Test markets data
        test_markets_data()
        
        print("\n🎉 All tests completed!")
    else:
        print("\n❌ Basic API test failed. Please check your API key.")
