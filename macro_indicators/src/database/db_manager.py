"""
Database Manager for Macro Economic Indicators

This module handles database operations for storing and retrieving macro economic indicators.
"""

import os
import sqlite3
import logging
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Union, Any, Tuple

from src.schema.data_schema import MacroIndicator, MacroIndicatorObservation, DATABASE_SCHEMA

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Database manager for macro economic indicators.
    
    This class handles database operations for storing and retrieving macro economic indicators.
    """
    
    def __init__(self, db_path: str):
        """
        Initialize the database manager.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self._init_database()
        logger.info(f"Initialized database manager with database at {db_path}")
    
    def _init_database(self):
        """Initialize the SQLite database with required tables"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create tables
            for table_name, schema in DATABASE_SCHEMA.items():
                cursor.execute(schema)
            
            conn.commit()
            conn.close()
            logger.info(f"Database initialized at {self.db_path}")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def save_indicator(self, indicator: MacroIndicator) -> bool:
        """
        Save indicator metadata to database.
        
        Args:
            indicator: MacroIndicator object
            
        Returns:
            True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Convert indicator to dictionary
            indicator_dict = indicator.to_dict()
            
            # Build SQL query
            columns = ', '.join(indicator_dict.keys())
            placeholders = ', '.join(['?' for _ in indicator_dict.keys()])
            values = tuple(indicator_dict.values())
            
            query = f"""
            INSERT OR REPLACE INTO indicators ({columns})
            VALUES ({placeholders})
            """
            
            cursor.execute(query, values)
            conn.commit()
            conn.close()
            
            logger.info(f"Saved indicator {indicator.name} ({indicator.indicator_id})")
            return True
        except Exception as e:
            logger.error(f"Error saving indicator {indicator.indicator_id}: {str(e)}")
            return False
    
    def save_observations(self, observations: List[MacroIndicatorObservation]) -> int:
        """
        Save multiple observations to database.
        
        Args:
            observations: List of MacroIndicatorObservation objects
            
        Returns:
            Number of observations saved
        """
        if not observations:
            return 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            
            for observation in observations:
                # Convert observation to dictionary
                obs_dict = observation.to_dict()
                
                # Build SQL query
                columns = ', '.join(obs_dict.keys())
                placeholders = ', '.join(['?' for _ in obs_dict.keys()])
                values = tuple(obs_dict.values())
                
                query = f"""
                INSERT OR REPLACE INTO observations ({columns})
                VALUES ({placeholders})
                """
                
                cursor.execute(query, values)
                saved_count += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"Saved {saved_count} observations")
            return saved_count
        except Exception as e:
            logger.error(f"Error saving observations: {str(e)}")
            return 0
    
    def save_dataframe_observations(self, indicator_id: str, df: pd.DataFrame,
                                  date_column: str = 'date',
                                  value_column: str = 'value') -> int:
        """
        Save observations from a DataFrame.
        
        Args:
            indicator_id: Indicator ID
            df: DataFrame with observations
            date_column: Name of date column
            value_column: Name of value column
            
        Returns:
            Number of observations saved
        """
        if df.empty:
            return 0
        
        try:
            observations = []
            
            # Calculate changes
            if len(df) > 1:
                df = df.sort_values(by=date_column)
                df['previous_value'] = df[value_column].shift(1)
                df['change'] = df[value_column] - df['previous_value']
                df['percent_change'] = (df['change'] / df['previous_value']) * 100 if 'previous_value' in df else None
            
            # Convert DataFrame rows to MacroIndicatorObservation objects
            for _, row in df.iterrows():
                observation = MacroIndicatorObservation(
                    indicator_id=indicator_id,
                    date=row[date_column],
                    value=row[value_column],
                    previous_value=row.get('previous_value'),
                    change=row.get('change'),
                    percent_change=row.get('percent_change')
                )
                observations.append(observation)
            
            # Save observations
            return self.save_observations(observations)
        except Exception as e:
            logger.error(f"Error saving DataFrame observations for {indicator_id}: {str(e)}")
            return 0
    
    def get_indicator(self, indicator_id: str) -> Optional[MacroIndicator]:
        """
        Get indicator metadata from database.
        
        Args:
            indicator_id: Indicator ID
            
        Returns:
            MacroIndicator object if found, None otherwise
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM indicators WHERE indicator_id = ?"
            cursor.execute(query, (indicator_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                # Convert row to dictionary
                indicator_dict = {key: row[key] for key in row.keys()}
                return MacroIndicator.from_dict(indicator_dict)
            else:
                return None
        except Exception as e:
            logger.error(f"Error getting indicator {indicator_id}: {str(e)}")
            return None
    
    def get_observations(self, indicator_id: str, 
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None,
                       limit: Optional[int] = None) -> List[MacroIndicatorObservation]:
        """
        Get observations for an indicator.
        
        Args:
            indicator_id: Indicator ID
            start_date: Start date in format 'YYYY-MM-DD'
            end_date: End date in format 'YYYY-MM-DD'
            limit: Maximum number of observations to return
            
        Returns:
            List of MacroIndicatorObservation objects
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM observations WHERE indicator_id = ?"
            params = [indicator_id]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
            
            query += " ORDER BY date DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, params)
            
            rows = cursor.fetchall()
            conn.close()
            
            observations = []
            for row in rows:
                # Convert row to dictionary
                obs_dict = {key: row[key] for key in row.keys()}
                observations.append(MacroIndicatorObservation.from_dict(obs_dict))
            
            return observations
        except Exception as e:
            logger.error(f"Error getting observations for {indicator_id}: {str(e)}")
            return []
    
    def get_observations_dataframe(self, indicator_id: str,
                                 start_date: Optional[str] = None,
                                 end_date: Optional[str] = None,
                                 limit: Optional[int] = None) -> pd.DataFrame:
        """
        Get observations for an indicator as a DataFrame.
        
        Args:
            indicator_id: Indicator ID
            start_date: Start date in format 'YYYY-MM-DD'
            end_date: End date in format 'YYYY-MM-DD'
            limit: Maximum number of observations to return
            
        Returns:
            DataFrame with observations
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = "SELECT * FROM observations WHERE indicator_id = ?"
            params = [indicator_id]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
            
            query += " ORDER BY date DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # Convert date column to datetime
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
            
            return df
        except Exception as e:
            logger.error(f"Error getting observations DataFrame for {indicator_id}: {str(e)}")
            return pd.DataFrame()
    
    def get_all_indicators(self) -> List[MacroIndicator]:
        """
        Get all indicators from database.
        
        Returns:
            List of MacroIndicator objects
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM indicators"
            cursor.execute(query)
            
            rows = cursor.fetchall()
            conn.close()
            
            indicators = []
            for row in rows:
                # Convert row to dictionary
                indicator_dict = {key: row[key] for key in row.keys()}
                indicators.append(MacroIndicator.from_dict(indicator_dict))
            
            return indicators
        except Exception as e:
            logger.error(f"Error getting all indicators: {str(e)}")
            return []
    
    def get_indicators_by_category(self, category: str) -> List[MacroIndicator]:
        """
        Get indicators by category.
        
        Args:
            category: Category name
            
        Returns:
            List of MacroIndicator objects
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM indicators WHERE category = ?"
            cursor.execute(query, (category,))
            
            rows = cursor.fetchall()
            conn.close()
            
            indicators = []
            for row in rows:
                # Convert row to dictionary
                indicator_dict = {key: row[key] for key in row.keys()}
                indicators.append(MacroIndicator.from_dict(indicator_dict))
            
            return indicators
        except Exception as e:
            logger.error(f"Error getting indicators for category {category}: {str(e)}")
            return []
    
    def update_indicator_latest_value(self, indicator_id: str, latest_value: float,
                                    latest_date: Union[str, datetime]) -> bool:
        """
        Update the latest value for an indicator.
        
        Args:
            indicator_id: Indicator ID
            latest_value: Latest value
            latest_date: Date of latest value
            
        Returns:
            True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get previous value
            cursor.execute(
                "SELECT latest_value FROM indicators WHERE indicator_id = ?",
                (indicator_id,)
            )
            row = cursor.fetchone()
            previous_value = row[0] if row else None
            
            # Calculate change
            change = None
            percent_change = None
            if previous_value is not None and latest_value is not None:
                change = latest_value - previous_value
                if previous_value != 0:
                    percent_change = (change / previous_value) * 100
            
            # Format date if needed
            if isinstance(latest_date, datetime):
                latest_date = latest_date.isoformat()
            
            # Update indicator
            cursor.execute(
                """
                UPDATE indicators
                SET latest_value = ?,
                    previous_value = ?,
                    change = ?,
                    percent_change = ?,
                    last_observation_date = ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE indicator_id = ?
                """,
                (latest_value, previous_value, change, percent_change, latest_date, indicator_id)
            )
            
            conn.commit()
            conn.close()
            
            logger.info(f"Updated latest value for {indicator_id}: {latest_value}")
            return True
        except Exception as e:
            logger.error(f"Error updating latest value for {indicator_id}: {str(e)}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dictionary with database statistics
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get indicator count
            cursor.execute("SELECT COUNT(*) FROM indicators")
            indicator_count = cursor.fetchone()[0]
            
            # Get observation count
            cursor.execute("SELECT COUNT(*) FROM observations")
            observation_count = cursor.fetchone()[0]
            
            # Get category counts
            cursor.execute("SELECT category, COUNT(*) FROM indicators GROUP BY category")
            category_counts = {row[0]: row[1] for row in cursor.fetchall()}
            
            # Get database size
            db_size = os.path.getsize(self.db_path)
            
            conn.close()
            
            return {
                'indicator_count': indicator_count,
                'observation_count': observation_count,
                'category_counts': category_counts,
                'database_size_bytes': db_size,
                'database_size_mb': db_size / (1024 * 1024),
                'database_path': self.db_path,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting database stats: {str(e)}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
