"""
Configuration settings for the Macro Economic Indicators project.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Keys
TRADING_ECONOMICS_API_KEY = os.getenv('TRADING_ECONOMICS')
FRED_API_KEY = os.getenv('FRED_API_KEY')
ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')

# Data Collection Settings
DEFAULT_START_DATE = '2010-01-01'  # Default start date for historical data
DEFAULT_END_DATE = None  # None means current date
UPDATE_FREQUENCY = 'daily'  # Options: 'daily', 'weekly', 'monthly'

# Database Settings
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'macro_indicators.db')

# Indicator Categories
INDICATOR_CATEGORIES = {
    'interest_rates': [
        'Federal Funds Rate',
        '10-Year Treasury Yield',
        '2-Year Treasury Yield',
        'Yield Curve Spread (10Y-2Y)',
    ],
    'economic_growth': [
        'GDP Growth Rate',
        'Unemployment Rate',
        'Non-Farm Payrolls',
        'Manufacturing PMI',
        'Services PMI',
        'Consumer Confidence',
        'Retail Sales',
        'Industrial Production',
    ],
    'inflation': [
        'Consumer Price Index',
        'Core CPI',
        'Producer Price Index',
        'PCE Price Index',
        'Core PCE',
    ],
    'currency': [
        'US Dollar Index',
        'EUR/USD Exchange Rate',
        'GBP/USD Exchange Rate',
        'USD/JPY Exchange Rate',
        'USD/CNY Exchange Rate',
    ],
    'commodities': [
        'Crude Oil Prices (WTI)',
        'Gold Prices',
        'Copper Prices',
        'Natural Gas Prices',
    ],
    'market_sentiment': [
        'VIX (Volatility Index)',
        'Credit Spreads (Investment Grade)',
        'Credit Spreads (High Yield)',
        'Market Breadth',
    ],
}

# Country Codes for Trading Economics
COUNTRY_CODES = {
    'united_states': 'united states',
    'euro_area': 'euro area',
    'china': 'china',
    'japan': 'japan',
    'united_kingdom': 'united kingdom',
    'canada': 'canada',
    'australia': 'australia',
    'india': 'india',
    'brazil': 'brazil',
    'mexico': 'mexico',
}

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'macro_indicators.log')
