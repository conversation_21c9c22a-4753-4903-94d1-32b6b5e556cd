#!/usr/bin/env python3
"""
Test script for the FRED Collector
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from collectors.fred_collector import FREDCollector
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_fred_collector():
    """Test the FRED collector functionality"""
    
    print("🧪 Testing FRED Collector")
    print("=" * 50)
    
    # Initialize collector with database
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators.db')
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    collector = FREDCollector(db_path=db_path)
    
    # Test with a subset of key indicators
    test_indicators = {
        'Federal Funds Rate': 'FEDFUNDS',
        'Unemployment Rate': 'UNRATE',
        'Consumer Price Index': 'CPIAUCSL',
        '10-Year Treasury Yield': 'DGS10',
        'GDP': 'GDP'
    }
    
    print(f"\n📊 Collecting data for {len(test_indicators)} indicators...")
    
    # Collect data for the last 5 years
    start_date = '2020-01-01'
    results = collector.collect_us_indicators(
        indicators=test_indicators,
        start_date=start_date,
        save_to_db=True
    )
    
    print(f"\n✅ Successfully collected data for {len(results)} indicators")
    
    # Display summary of collected data
    for name, data in results.items():
        if not data.empty:
            latest_value = data.iloc[0]['value']
            latest_date = data.iloc[0]['date']
            print(f"  📈 {name}: {latest_value} (as of {latest_date.strftime('%Y-%m-%d')})")
    
    print(f"\n💾 Data saved to database: {db_path}")
    
    return results

def test_specific_indicator():
    """Test getting data for a specific indicator"""
    
    print("\n" + "=" * 50)
    print("🔍 Testing specific indicator collection")
    
    collector = FREDCollector()
    
    # Test getting GDP data
    print("\n📊 Getting GDP data...")
    gdp_data = collector.get_series_data('GDP', start_date='2015-01-01')
    
    if not gdp_data.empty:
        print(f"✅ GDP data: {len(gdp_data)} observations")
        print(f"📊 Recent GDP values:")
        for _, row in gdp_data.head(5).iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: ${row['value']:.1f} billion")
    
    # Test getting series info
    print("\n📋 Getting series metadata...")
    gdp_info = collector.get_series_info('GDP')
    
    if gdp_info:
        print(f"✅ GDP metadata:")
        print(f"  Title: {gdp_info.get('title', 'N/A')}")
        print(f"  Units: {gdp_info.get('units', 'N/A')}")
        print(f"  Frequency: {gdp_info.get('frequency', 'N/A')}")
        print(f"  Last Updated: {gdp_info.get('last_updated', 'N/A')}")

if __name__ == "__main__":
    # Test the collector
    results = test_fred_collector()
    
    # Test specific indicator
    test_specific_indicator()
    
    print("\n🎉 All tests completed!")
    print(f"📊 Collected data for {len(results)} indicators")
