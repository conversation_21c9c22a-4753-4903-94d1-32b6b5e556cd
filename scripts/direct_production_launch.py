#!/usr/bin/env python3
"""
Direct Production Session Launcher
Bypasses any caching issues and directly starts the 8-hour production session
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.data_pipeline.orchestrators.comprehensive_data_orchestrator import (
    ComprehensiveDataOrchestrator, CollectionSession
)

def main():
    """Launch production session directly"""
    print("🚀 DIRECT PRODUCTION SESSION LAUNCHER")
    print("=" * 60)
    
    # Create production session configuration
    session = CollectionSession(
        session_id=f"production_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        start_time=datetime.now(),
        duration_hours=8,
        target_symbols=[
            # Major Tech (High Priority)
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA',
            
            # Major Finance
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BRK-B', 'V', 'MA',
            
            # Major Healthcare
            'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'DHR', 'BMY',
            
            # Major Consumer
            'HD', 'MCD', 'NKE', 'COST', 'WMT', 'PG', 'KO', 'PEP',
            
            # Major Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX',
            
            # Major Industrial
            'CAT', 'BA', 'HON', 'UPS', 'RTX', 'LMT',
            
            # Major ETFs
            'SPY', 'QQQ', 'IWM', 'VTI', 'VEA', 'VWO', 'GLD', 'TLT',
            
            # Sector ETFs
            'XLK', 'XLF', 'XLV', 'XLE', 'XLI', 'XLY', 'XLP', 'XLB', 'XLU', 'XLRE'
        ],
        data_sources=[
            'eodhd',                    # Priority 1: Comprehensive data
            'finnhub',                  # Priority 2: Real-time + fundamentals
            'financial_modeling_prep',  # Priority 3: Deep fundamentals
            'yahoo_finance',            # Priority 4: Reliable historical
            'alpha_vantage',           # Priority 5: Technical indicators
            'polygon'                  # Priority 6: High-quality market data
        ]
    )
    
    print(f"📊 Session ID: {session.session_id}")
    print(f"⏱️ Duration: {session.duration_hours} hours")
    print(f"🏢 Target symbols: {len(session.target_symbols)}")
    print(f"🔧 Data sources: {len(session.data_sources)}")
    print(f"🚀 Start time: {session.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📊 Symbols to collect:")
    for i, symbol in enumerate(session.target_symbols):
        if i % 10 == 0:
            print()
        print(f"{symbol:>6}", end=" ")
    
    print(f"\n\n🔧 Data sources (in priority order):")
    for i, source in enumerate(session.data_sources, 1):
        print(f"   {i}. {source}")
    
    print("\n" + "=" * 60)
    
    # Confirmation
    print("⚠️  PRODUCTION MODE - This will run for 8 hours!")
    print("   - Make sure your system can run uninterrupted")
    print("   - Ensure stable internet connection")
    print("   - Monitor disk space for data storage")
    
    confirm = input("\n🤔 Continue with production session? (yes/no): ").lower().strip()
    if confirm not in ['yes', 'y']:
        print("❌ Session cancelled by user")
        return
    
    # Initialize and start orchestrator
    print(f"\n🚀 Starting 8-hour production data collection session...")
    print("   Press Ctrl+C to gracefully stop the session")
    
    try:
        orchestrator = ComprehensiveDataOrchestrator(session)
        results = orchestrator.start_8_hour_session()
        
        # Display final results
        print("\n🎉 SESSION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        
        summary = results['session_summary']
        print(f"📊 Session ID: {summary['session_id']}")
        print(f"⏱️ Duration: {summary['duration_hours']:.2f} hours")
        print(f"📈 Records collected: {summary['total_records_collected']:,}")
        print(f"📞 API calls made: {summary['total_api_calls']:,}")
        print(f"❌ Errors: {summary['errors_encountered']}")
        print(f"📊 Records/hour: {summary['records_per_hour']:.0f}")
        print(f"🔧 API efficiency: {results['data_quality_metrics']['api_efficiency']:.2f} records/call")
        
        # Source performance
        print(f"\n📊 SOURCE PERFORMANCE:")
        for source, stats in results['source_performance'].items():
            print(f"   {source}: {stats['total_records']:,} records, "
                  f"{stats['success_rate']:.1%} success")
        
        # Save final summary
        summary_file = Path(f"session_summary_{summary['session_id']}.json")
        with open(summary_file, 'w') as f:
            import json
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Full results saved to: {summary_file}")
        
    except KeyboardInterrupt:
        print("\n🛑 Session interrupted by user")
        print("💾 Data collected so far has been saved")
        
    except Exception as e:
        print(f"\n❌ Session failed: {str(e)}")
        print("💾 Partial data may have been saved")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
