#!/usr/bin/env python3
"""
Export macro indicator data to CSV files for analysis
"""

import os
import sqlite3
import pandas as pd
from datetime import datetime
import argparse

def export_observations_to_csv(db_path: str, output_dir: str = 'exports'):
    """
    Export all observations to CSV files.
    
    Args:
        db_path: Path to SQLite database
        output_dir: Output directory for CSV files
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    print("📊 Exporting macro indicator data to CSV...")
    
    # Get all unique indicators
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT indicator_id FROM observations ORDER BY indicator_id")
    indicators = [row[0] for row in cursor.fetchall()]
    
    print(f"📋 Found {len(indicators)} indicators with data")
    
    # Export individual indicator files
    individual_exports = []
    
    for indicator_id in indicators:
        try:
            # Get observations for this indicator
            query = """
            SELECT 
                indicator_id,
                date,
                value,
                change,
                percent_change
            FROM observations 
            WHERE indicator_id = ?
            ORDER BY date DESC
            """
            
            df = pd.read_sql_query(query, conn, params=(indicator_id,))
            
            if not df.empty:
                # Convert date column
                df['date'] = pd.to_datetime(df['date'])
                
                # Export to CSV
                filename = f"{indicator_id}_data.csv"
                filepath = os.path.join(output_dir, filename)
                df.to_csv(filepath, index=False)
                
                individual_exports.append({
                    'indicator_id': indicator_id,
                    'filename': filename,
                    'observations': len(df),
                    'date_range': f"{df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}",
                    'latest_value': df.iloc[0]['value'] if len(df) > 0 else None,
                    'latest_date': df.iloc[0]['date'].strftime('%Y-%m-%d') if len(df) > 0 else None
                })
                
                print(f"  ✅ {indicator_id}: {len(df)} observations -> {filename}")
        
        except Exception as e:
            print(f"  ❌ Error exporting {indicator_id}: {str(e)}")
    
    # Export combined data file
    print("\n📊 Creating combined data file...")
    
    try:
        # Get all observations with indicator metadata
        combined_query = """
        SELECT 
            o.indicator_id,
            i.name as indicator_name,
            i.category,
            i.units,
            i.frequency,
            o.date,
            o.value,
            o.change,
            o.percent_change
        FROM observations o
        LEFT JOIN indicators i ON o.indicator_id = i.series_id
        ORDER BY o.indicator_id, o.date DESC
        """
        
        combined_df = pd.read_sql_query(combined_query, conn)
        
        if not combined_df.empty:
            # Convert date column
            combined_df['date'] = pd.to_datetime(combined_df['date'])
            
            # Export combined file
            combined_filepath = os.path.join(output_dir, 'all_indicators_combined.csv')
            combined_df.to_csv(combined_filepath, index=False)
            
            print(f"  ✅ Combined data: {len(combined_df)} total observations -> all_indicators_combined.csv")
        
    except Exception as e:
        print(f"  ❌ Error creating combined file: {str(e)}")
    
    # Export pivot table (indicators as columns)
    print("\n📊 Creating pivot table format...")
    
    try:
        # Create pivot table with indicators as columns
        pivot_query = """
        SELECT 
            date,
            indicator_id,
            value
        FROM observations
        ORDER BY date DESC, indicator_id
        """
        
        pivot_df = pd.read_sql_query(pivot_query, conn)
        
        if not pivot_df.empty:
            # Convert date column
            pivot_df['date'] = pd.to_datetime(pivot_df['date'])
            
            # Create pivot table
            pivot_table = pivot_df.pivot(index='date', columns='indicator_id', values='value')
            
            # Sort by date descending
            pivot_table = pivot_table.sort_index(ascending=False)
            
            # Export pivot table
            pivot_filepath = os.path.join(output_dir, 'indicators_pivot_table.csv')
            pivot_table.to_csv(pivot_filepath)
            
            print(f"  ✅ Pivot table: {len(pivot_table)} dates x {len(pivot_table.columns)} indicators -> indicators_pivot_table.csv")
        
    except Exception as e:
        print(f"  ❌ Error creating pivot table: {str(e)}")
    
    # Export summary statistics
    print("\n📊 Creating summary statistics...")
    
    try:
        summary_data = []
        
        for export in individual_exports:
            summary_data.append(export)
        
        summary_df = pd.DataFrame(summary_data)
        
        if not summary_df.empty:
            # Export summary
            summary_filepath = os.path.join(output_dir, 'export_summary.csv')
            summary_df.to_csv(summary_filepath, index=False)
            
            print(f"  ✅ Export summary: {len(summary_df)} indicators -> export_summary.csv")
    
    except Exception as e:
        print(f"  ❌ Error creating summary: {str(e)}")
    
    conn.close()
    
    return individual_exports

def export_indicator_metadata(db_path: str, output_dir: str = 'exports'):
    """
    Export indicator metadata to CSV.
    
    Args:
        db_path: Path to SQLite database
        output_dir: Output directory for CSV files
    """
    try:
        conn = sqlite3.connect(db_path)
        
        # Get indicator metadata
        metadata_query = """
        SELECT 
            series_id as indicator_id,
            name,
            category,
            units,
            frequency,
            description,
            last_updated,
            metadata
        FROM indicators
        ORDER BY category, name
        """
        
        metadata_df = pd.read_sql_query(metadata_query, conn)
        
        if not metadata_df.empty:
            # Export metadata
            metadata_filepath = os.path.join(output_dir, 'indicator_metadata.csv')
            metadata_df.to_csv(metadata_filepath, index=False)
            
            print(f"  ✅ Indicator metadata: {len(metadata_df)} indicators -> indicator_metadata.csv")
        
        conn.close()
        
    except Exception as e:
        print(f"  ❌ Error exporting metadata: {str(e)}")

def create_analysis_ready_dataset(db_path: str, output_dir: str = 'exports'):
    """
    Create analysis-ready dataset with key indicators.
    
    Args:
        db_path: Path to SQLite database
        output_dir: Output directory for CSV files
    """
    try:
        conn = sqlite3.connect(db_path)
        
        # Key indicators for analysis
        key_indicators = [
            'FEDFUNDS',    # Federal Funds Rate
            'DGS10',       # 10-Year Treasury
            'DGS2',        # 2-Year Treasury
            'UNRATE',      # Unemployment Rate
            'CPIAUCSL',    # CPI
            'GDP',         # GDP
            'PAYEMS',      # Nonfarm Payrolls
            'INDPRO',      # Industrial Production
            'HOUST',       # Housing Starts
            'UMCSENT'      # Consumer Sentiment
        ]
        
        # Get data for key indicators
        key_indicators_str = "', '".join(key_indicators)
        analysis_query = f"""
        SELECT 
            o.date,
            o.indicator_id,
            i.name as indicator_name,
            i.category,
            i.units,
            o.value,
            o.change,
            o.percent_change
        FROM observations o
        LEFT JOIN indicators i ON o.indicator_id = i.series_id
        WHERE o.indicator_id IN ('{key_indicators_str}')
        ORDER BY o.date DESC, o.indicator_id
        """
        
        analysis_df = pd.read_sql_query(analysis_query, conn)
        
        if not analysis_df.empty:
            # Convert date column
            analysis_df['date'] = pd.to_datetime(analysis_df['date'])
            
            # Export analysis dataset
            analysis_filepath = os.path.join(output_dir, 'key_indicators_analysis.csv')
            analysis_df.to_csv(analysis_filepath, index=False)
            
            print(f"  ✅ Key indicators analysis: {len(analysis_df)} observations -> key_indicators_analysis.csv")
            
            # Create pivot for key indicators
            key_pivot = analysis_df.pivot(index='date', columns='indicator_id', values='value')
            key_pivot = key_pivot.sort_index(ascending=False)
            
            key_pivot_filepath = os.path.join(output_dir, 'key_indicators_pivot.csv')
            key_pivot.to_csv(key_pivot_filepath)
            
            print(f"  ✅ Key indicators pivot: {len(key_pivot)} dates x {len(key_pivot.columns)} indicators -> key_indicators_pivot.csv")
        
        conn.close()
        
    except Exception as e:
        print(f"  ❌ Error creating analysis dataset: {str(e)}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Export macro indicator data to CSV')
    
    parser.add_argument(
        '--db-path',
        type=str,
        default=os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators.db'),
        help='Path to SQLite database'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default=os.path.join(os.path.dirname(__file__), 'exports'),
        help='Output directory for CSV files'
    )
    
    args = parser.parse_args()
    
    # Check if database exists
    if not os.path.exists(args.db_path):
        print(f"❌ Database not found: {args.db_path}")
        return
    
    print("🚀 Starting CSV export process...")
    print(f"📊 Database: {args.db_path}")
    print(f"📁 Output directory: {args.output_dir}")
    print("=" * 50)
    
    # Export observations
    exports = export_observations_to_csv(args.db_path, args.output_dir)
    
    # Export metadata
    export_indicator_metadata(args.db_path, args.output_dir)
    
    # Create analysis-ready dataset
    create_analysis_ready_dataset(args.db_path, args.output_dir)
    
    print("\n" + "=" * 50)
    print("✅ CSV export completed!")
    print(f"📁 Files exported to: {args.output_dir}")
    print(f"📊 Individual indicator files: {len(exports)}")
    print(f"📋 Summary files: 6 (combined, pivot, metadata, analysis, etc.)")
    
    # Show file sizes
    total_size = 0
    for root, dirs, files in os.walk(args.output_dir):
        for file in files:
            if file.endswith('.csv'):
                filepath = os.path.join(root, file)
                size = os.path.getsize(filepath)
                total_size += size
    
    print(f"💾 Total export size: {total_size / (1024*1024):.2f} MB")

if __name__ == "__main__":
    main()
