"""
Data Schema for Macro Economic Indicators

This module defines the data schema and structures for the macro economic indicators project.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import json

@dataclass
class MacroIndicator:
    """
    Data class representing a macro economic indicator.
    """
    # Core identifier fields
    indicator_id: str  # Unique identifier (e.g., 'FEDFUNDS')
    name: str  # Human-readable name (e.g., 'Federal Funds Rate')
    
    # Classification fields
    category: str  # Primary category (e.g., 'interest_rates')
    subcategory: Optional[str] = None  # Optional subcategory
    country: str = 'US'  # Country code
    region: Optional[str] = None  # Optional region within country
    
    # Metadata fields
    description: Optional[str] = None  # Detailed description
    units: Optional[str] = None  # Units of measurement (e.g., 'percent')
    frequency: Optional[str] = None  # Data frequency (e.g., 'monthly')
    seasonal_adjustment: Optional[str] = None  # Seasonal adjustment info
    source: Optional[str] = None  # Data source (e.g., 'FRED')
    source_url: Optional[str] = None  # URL to source
    notes: Optional[str] = None  # Additional notes
    
    # Temporal fields
    last_updated: Optional[datetime] = None  # Last update timestamp
    first_observation_date: Optional[datetime] = None  # First available data point
    last_observation_date: Optional[datetime] = None  # Most recent data point
    
    # Data fields
    latest_value: Optional[float] = None  # Most recent value
    previous_value: Optional[float] = None  # Previous period value
    change: Optional[float] = None  # Change from previous period
    percent_change: Optional[float] = None  # Percent change from previous period
    
    # Trend fields
    trend_direction: Optional[str] = None  # 'increasing', 'decreasing', 'stable'
    trend_strength: Optional[float] = None  # Strength of trend (0-1)
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)  # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, dict):
                result[key] = json.dumps(value)
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MacroIndicator':
        """Create from dictionary"""
        # Handle datetime fields
        for date_field in ['last_updated', 'first_observation_date', 'last_observation_date']:
            if date_field in data and data[date_field] and isinstance(data[date_field], str):
                try:
                    data[date_field] = datetime.fromisoformat(data[date_field])
                except ValueError:
                    data[date_field] = None
        
        # Handle metadata field
        if 'metadata' in data and isinstance(data['metadata'], str):
            try:
                data['metadata'] = json.loads(data['metadata'])
            except json.JSONDecodeError:
                data['metadata'] = {}
        
        return cls(**data)

@dataclass
class MacroIndicatorObservation:
    """
    Data class representing a single observation of a macro economic indicator.
    """
    # Core identifier fields
    indicator_id: str  # Indicator ID this observation belongs to
    date: datetime  # Date of observation
    
    # Value fields
    value: float  # Observed value
    original_value: Optional[float] = None  # Original value before any transformations
    
    # Quality fields
    is_estimate: bool = False  # Whether this is an estimated value
    is_forecast: bool = False  # Whether this is a forecasted value
    is_revised: bool = False  # Whether this value has been revised
    previous_value: Optional[float] = None  # Previous value if revised
    
    # Calculation fields
    change: Optional[float] = None  # Change from previous period
    percent_change: Optional[float] = None  # Percent change from previous period
    
    # Metadata
    notes: Optional[str] = None  # Any notes about this observation
    metadata: Dict[str, Any] = field(default_factory=dict)  # Additional metadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, dict):
                result[key] = json.dumps(value)
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MacroIndicatorObservation':
        """Create from dictionary"""
        # Handle datetime fields
        if 'date' in data and isinstance(data['date'], str):
            try:
                data['date'] = datetime.fromisoformat(data['date'])
            except ValueError:
                data['date'] = datetime.now()  # Fallback
        
        # Handle metadata field
        if 'metadata' in data and isinstance(data['metadata'], str):
            try:
                data['metadata'] = json.loads(data['metadata'])
            except json.JSONDecodeError:
                data['metadata'] = {}
        
        return cls(**data)

# Define indicator categories
INDICATOR_CATEGORIES = {
    'interest_rates': 'Interest Rates & Monetary Policy',
    'economic_growth': 'Economic Growth & Activity',
    'inflation': 'Inflation & Prices',
    'employment': 'Employment & Labor Market',
    'housing': 'Housing & Construction',
    'consumer': 'Consumer Spending & Confidence',
    'business': 'Business Activity & Sentiment',
    'trade': 'Trade & International',
    'government': 'Government & Fiscal Policy',
    'financial_markets': 'Financial Markets',
    'commodities': 'Commodities & Resources',
    'banking': 'Banking & Credit',
}

# Define data frequencies
DATA_FREQUENCIES = {
    'D': 'Daily',
    'W': 'Weekly',
    'M': 'Monthly',
    'Q': 'Quarterly',
    'SA': 'Semi-Annual',
    'A': 'Annual',
}

# Define trend directions
TREND_DIRECTIONS = {
    'increasing': 'Increasing',
    'decreasing': 'Decreasing',
    'stable': 'Stable',
    'volatile': 'Volatile',
    'unknown': 'Unknown',
}

# Define database schema (for reference)
DATABASE_SCHEMA = {
    'indicators': '''
        CREATE TABLE IF NOT EXISTS indicators (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            indicator_id TEXT NOT NULL,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            subcategory TEXT,
            country TEXT NOT NULL,
            region TEXT,
            description TEXT,
            units TEXT,
            frequency TEXT,
            seasonal_adjustment TEXT,
            source TEXT,
            source_url TEXT,
            notes TEXT,
            last_updated TEXT,
            first_observation_date TEXT,
            last_observation_date TEXT,
            latest_value REAL,
            previous_value REAL,
            change REAL,
            percent_change REAL,
            trend_direction TEXT,
            trend_strength REAL,
            metadata TEXT,
            UNIQUE(indicator_id)
        )
    ''',
    
    'observations': '''
        CREATE TABLE IF NOT EXISTS observations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            indicator_id TEXT NOT NULL,
            date TEXT NOT NULL,
            value REAL NOT NULL,
            original_value REAL,
            is_estimate INTEGER DEFAULT 0,
            is_forecast INTEGER DEFAULT 0,
            is_revised INTEGER DEFAULT 0,
            previous_value REAL,
            change REAL,
            percent_change REAL,
            notes TEXT,
            metadata TEXT,
            UNIQUE(indicator_id, date)
        )
    ''',
    
    'categories': '''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            parent_category_id TEXT,
            UNIQUE(category_id)
        )
    ''',
    
    'countries': '''
        CREATE TABLE IF NOT EXISTS countries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            country_code TEXT NOT NULL,
            name TEXT NOT NULL,
            region TEXT,
            UNIQUE(country_code)
        )
    ''',
}
