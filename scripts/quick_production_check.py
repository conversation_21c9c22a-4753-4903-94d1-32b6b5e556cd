#!/usr/bin/env python3
"""
Quick Production Readiness Check
Essential validation before 8-hour production session
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def check_critical_systems():
    """Check critical systems for production readiness"""
    print("🔍 QUICK PRODUCTION READINESS CHECK")
    print("=" * 50)
    
    checks = []
    
    # 1. Check API Key Manager
    try:
        from src.data_pipeline.utils.multi_api_key_manager import MultiAPIKeyManager
        key_manager = MultiAPIKeyManager()
        
        # Count active keys
        sources = ['eodhd', 'finnhub', 'financial_modeling_prep', 'yahoo_finance', 'alpha_vantage', 'polygon']
        active_keys = {}
        total_keys = 0
        
        for source in sources:
            keys = key_manager.get_available_keys(source)
            active_keys[source] = len(keys)
            total_keys += len(keys)
        
        print(f"✅ API Key Manager: {total_keys} active keys")
        for source, count in active_keys.items():
            print(f"   {source}: {count} keys")
        
        checks.append(("API Key Manager", True, f"{total_keys} active keys"))
        
    except Exception as e:
        print(f"❌ API Key Manager: {str(e)}")
        checks.append(("API Key Manager", False, str(e)))
    
    # 2. Check Data Collectors
    collectors_to_test = [
        ('Finnhub', 'src.data_pipeline.collectors.finnhub_collector', 'FinnhubCollector'),
        ('Financial Modeling Prep', 'src.data_pipeline.collectors.financial_modeling_prep_collector', 'FinancialModelingPrepCollector'),
        ('Yahoo Finance', 'scripts.create_yahoo_collector', 'YahooFinanceCollector'),
        ('EODHD', 'src.data_pipeline.collectors.eodhd_collector', 'EODHDCollector'),
        ('Alpha Vantage', 'src.data_pipeline.collectors.alpha_vantage_collector', 'AlphaVantageCollector')
    ]
    
    working_collectors = 0
    for name, module_path, class_name in collectors_to_test:
        try:
            module = __import__(module_path, fromlist=[class_name])
            collector_class = getattr(module, class_name)
            collector = collector_class()
            print(f"✅ {name} Collector: OK")
            checks.append((f"{name} Collector", True, "Import successful"))
            working_collectors += 1
        except Exception as e:
            print(f"❌ {name} Collector: {str(e)}")
            checks.append((f"{name} Collector", False, str(e)))
    
    # 3. Check Orchestrator
    try:
        from src.data_pipeline.orchestrators.comprehensive_data_orchestrator import ComprehensiveDataOrchestrator
        print("✅ Comprehensive Data Orchestrator: OK")
        checks.append(("Orchestrator", True, "Import successful"))
    except Exception as e:
        print(f"❌ Comprehensive Data Orchestrator: {str(e)}")
        checks.append(("Orchestrator", False, str(e)))
    
    # 4. Check Multi-Source Integrator
    try:
        from src.data_pipeline.integrators.multi_source_integrator import MultiSourceDataIntegrator
        integrator = MultiSourceDataIntegrator()
        print(f"✅ Multi-Source Integrator: {len(integrator.collectors)} collectors")
        checks.append(("Multi-Source Integrator", True, f"{len(integrator.collectors)} collectors"))
    except Exception as e:
        print(f"❌ Multi-Source Integrator: {str(e)}")
        checks.append(("Multi-Source Integrator", False, str(e)))
    
    # 5. Check Production Scripts
    scripts = [
        'scripts/launch_8_hour_collection.py',
        'scripts/monitor_collection_session.py'
    ]
    
    for script in scripts:
        script_path = project_root / script
        if script_path.exists():
            print(f"✅ {script}: OK")
            checks.append((script, True, "File exists"))
        else:
            print(f"❌ {script}: Missing")
            checks.append((script, False, "File missing"))
    
    # 6. Check Data Directories
    data_dirs = ['data/orchestrator_sessions', 'data/raw', 'data/processed']
    for data_dir in data_dirs:
        dir_path = project_root / data_dir
        dir_path.mkdir(parents=True, exist_ok=True)
        if dir_path.exists():
            print(f"✅ {data_dir}: OK")
            checks.append((data_dir, True, "Directory exists"))
        else:
            print(f"❌ {data_dir}: Missing")
            checks.append((data_dir, False, "Directory missing"))
    
    # Summary
    print("\n" + "=" * 50)
    total_checks = len(checks)
    passed_checks = len([c for c in checks if c[1]])
    failed_checks = total_checks - passed_checks
    
    print(f"📊 SUMMARY:")
    print(f"   Total checks: {total_checks}")
    print(f"   ✅ Passed: {passed_checks}")
    print(f"   ❌ Failed: {failed_checks}")
    print(f"   Success rate: {(passed_checks/total_checks)*100:.1f}%")
    
    # Decision
    critical_systems = ["API Key Manager", "Orchestrator", "Multi-Source Integrator"]
    critical_passed = len([c for c in checks if c[0] in critical_systems and c[1]])
    
    if critical_passed == len(critical_systems) and passed_checks >= total_checks * 0.8:
        print(f"\n🚀 DECISION: GO - System ready for production")
        print(f"   Critical systems: {critical_passed}/{len(critical_systems)} working")
        print(f"   Overall readiness: {(passed_checks/total_checks)*100:.1f}%")
        return True
    else:
        print(f"\n🛑 DECISION: NO-GO - Critical issues found")
        print(f"   Critical systems: {critical_passed}/{len(critical_systems)} working")
        print(f"   Overall readiness: {(passed_checks/total_checks)*100:.1f}%")
        
        # Show failed critical systems
        failed_critical = [c for c in checks if c[0] in critical_systems and not c[1]]
        if failed_critical:
            print(f"\n❌ Failed critical systems:")
            for name, status, message in failed_critical:
                print(f"   - {name}: {message}")
        
        return False

def main():
    """Main function"""
    try:
        ready = check_critical_systems()
        return ready
    except Exception as e:
        print(f"\n❌ Check failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
