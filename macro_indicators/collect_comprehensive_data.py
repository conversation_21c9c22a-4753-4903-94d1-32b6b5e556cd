#!/usr/bin/env python3
"""
Comprehensive Macro Economic Data Collection

This script collects a wide range of macro indicators for sophisticated analysis:
- All major economic categories
- Leading, coincident, and lagging indicators
- High-frequency and low-frequency data
- Market-based and survey-based indicators
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.collectors.fred_collector import FREDCollector

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Comprehensive indicator set for macro analysis
COMPREHENSIVE_INDICATORS = {
    
    # ========== INTEREST RATES & MONETARY POLICY ==========
    'interest_rates': {
        'FEDFUNDS': 'Federal Funds Rate',
        'DGS3MO': '3-Month Treasury Rate',
        'DGS6MO': '6-Month Treasury Rate', 
        'DGS1': '1-Year Treasury Rate',
        'DGS2': '2-Year Treasury Rate',
        'DGS5': '5-Year Treasury Rate',
        'DGS10': '10-Year Treasury Rate',
        'DGS30': '30-Year Treasury Rate',
        'T10Y2Y': 'Yield Curve Spread (10Y-2Y)',
        'T10Y3M': 'Yield Curve Spread (10Y-3M)',
        'AAA': 'AAA Corporate Bond Yield',
        'BAA': 'BAA Corporate Bond Yield',
        'MORTGAGE30US': '30-Year Fixed Mortgage Rate',
        'TEDRATE': 'TED Spread',
    },
    
    # ========== ECONOMIC GROWTH & ACTIVITY ==========
    'economic_growth': {
        'GDP': 'Gross Domestic Product',
        'GDPC1': 'Real GDP',
        'A191RL1Q225SBEA': 'GDP Growth Rate (QoQ)',
        'INDPRO': 'Industrial Production Index',
        'TCU': 'Capacity Utilization',
        'CFNAI': 'Chicago Fed National Activity Index',
        'USSLIND': 'Leading Economic Index',
        'AHETPI': 'Average Hourly Earnings',
        'OPHNFB': 'Nonfarm Business Productivity',
        'ULCNFB': 'Unit Labor Costs',
    },
    
    # ========== EMPLOYMENT & LABOR MARKET ==========
    'employment': {
        'UNRATE': 'Unemployment Rate',
        'PAYEMS': 'Total Nonfarm Payrolls',
        'USPRIV': 'Private Nonfarm Payrolls',
        'MANEMP': 'Manufacturing Employment',
        'USCONS': 'Construction Employment',
        'CIVPART': 'Labor Force Participation Rate',
        'EMRATIO': 'Employment-Population Ratio',
        'ICSA': 'Initial Jobless Claims',
        'CCSA': 'Continuing Jobless Claims',
        'JTSJOL': 'Job Openings',
        'JTSQUR': 'Quits Rate',
        'JTSHIR': 'Hires Rate',
    },
    
    # ========== INFLATION & PRICES ==========
    'inflation': {
        'CPIAUCSL': 'Consumer Price Index',
        'CPILFESL': 'Core CPI',
        'PPIACO': 'Producer Price Index',
        'PPILFE': 'Core PPI',
        'PCEPI': 'PCE Price Index',
        'PCEPILFE': 'Core PCE Price Index',
        'GDPDEF': 'GDP Deflator',
        'T5YIE': '5-Year Breakeven Inflation Rate',
        'T10YIE': '10-Year Breakeven Inflation Rate',
        'MICH': 'University of Michigan Inflation Expectations',
    },
    
    # ========== CONSUMER SPENDING & CONFIDENCE ==========
    'consumer': {
        'PCE': 'Personal Consumption Expenditures',
        'PCEC1': 'Real Personal Consumption',
        'PI': 'Personal Income',
        'DSPI': 'Disposable Personal Income',
        'PSAVERT': 'Personal Saving Rate',
        'RSAFS': 'Retail Sales',
        'RSAFSNA': 'Retail Sales Ex Auto',
        'TOTALSA': 'Motor Vehicle Sales',
        'UMCSENT': 'Consumer Sentiment',
        'TOTALSL': 'Consumer Credit',
        'DRCCLACBS': 'Credit Card Delinquency Rate',
    },
    
    # ========== HOUSING & CONSTRUCTION ==========
    'housing': {
        'HOUST': 'Housing Starts',
        'HOUST1F': 'Single-Family Housing Starts',
        'PERMIT': 'Building Permits',
        'HSN1F': 'New Home Sales',
        'EXHOSLUSM495S': 'Existing Home Sales',
        'MSPUS': 'Median Home Price',
        'CSUSHPINSA': 'Case-Shiller Home Price Index',
        'RHORUSQ156N': 'Home Ownership Rate',
        'TTLCONS': 'Construction Spending',
        'TLRESCONS': 'Residential Construction',
    },
    
    # ========== BUSINESS ACTIVITY & SENTIMENT ==========
    'business': {
        'BUSINV': 'Business Inventories',
        'MNFCTI': 'Manufacturing Inventories',
        'DGORDER': 'Durable Goods Orders',
        'NEWORDER': 'Core Durable Goods Orders',
        'AMTMNO': 'Factory Orders',
        'PNFI': 'Business Investment',
        'CP': 'Corporate Profits',
        'CPATAX': 'Corporate Profits After Tax',
        'PHILLY': 'Philadelphia Fed Index',
        'GACDINA066MNFRBNY': 'NY Fed Empire State Index',
    },
    
    # ========== TRADE & INTERNATIONAL ==========
    'trade': {
        'BOPGSTB': 'Trade Balance',
        'EXPGS': 'Exports of Goods and Services',
        'IMPGS': 'Imports of Goods and Services',
        'NETFI': 'Current Account Balance',
        'DTWEXBGS': 'US Dollar Index',
        'DEXUSEU': 'EUR/USD Exchange Rate',
        'DEXUSUK': 'GBP/USD Exchange Rate',
        'DEXJPUS': 'USD/JPY Exchange Rate',
        'DEXCAUS': 'USD/CAD Exchange Rate',
    },
    
    # ========== GOVERNMENT & FISCAL POLICY ==========
    'government': {
        'GFDEBTN': 'Federal Debt',
        'GFDEGDQ188S': 'Federal Debt to GDP',
        'FYFSGDA188S': 'Federal Surplus/Deficit',
        'FGRECPT': 'Federal Receipts',
        'FGEXPND': 'Federal Outlays',
        'GCE': 'Government Consumption',
        'GPDIC1': 'Government Investment',
    },
    
    # ========== FINANCIAL MARKETS ==========
    'financial_markets': {
        'SP500': 'S&P 500',
        'NASDAQCOM': 'NASDAQ Composite',
        'DJIA': 'Dow Jones Industrial Average',
        'RU2000PR': 'Russell 2000',
        'VIXCLS': 'VIX Volatility Index',
        'BAMLH0A0HYM2': 'High Yield Credit Spread',
        'BAMLC0A0CM': 'Investment Grade Credit Spread',
        'THREEFYTP10': '10-Year Term Premium',
    },
    
    # ========== BANKING & CREDIT ==========
    'banking': {
        'TOTBKCR': 'Total Bank Credit',
        'BUSLOANS': 'Commercial & Industrial Loans',
        'REALLN': 'Real Estate Loans',
        'CONSUMER': 'Consumer Loans',
        'TOTRESNS': 'Bank Reserves',
        'M1SL': 'M1 Money Supply',
        'M2SL': 'M2 Money Supply',
        'M2V': 'Velocity of M2',
    },
    
    # ========== COMMODITIES & RESOURCES ==========
    'commodities': {
        'DCOILWTICO': 'WTI Crude Oil',
        'DHHNGSP': 'Natural Gas',
        'GOLDAMGBD228NLBM': 'Gold Price',
        'PCOPPUSDM': 'Copper Price',
        'PMAIZMTUSDM': 'Corn Price',
        'PWHEAMTUSDM': 'Wheat Price',
        'PSOYBUSDM': 'Soybeans Price',
    },
}

def collect_indicator_batch(collector: FREDCollector, indicators: dict, category: str, 
                          conn: sqlite3.Connection, start_date: str = '2010-01-01') -> dict:
    """
    Collect a batch of indicators for a specific category
    """
    
    batch_stats = {
        'category': category,
        'total': len(indicators),
        'successful': 0,
        'failed': 0,
        'total_observations': 0,
        'indicators': []
    }
    
    logger.info(f"🔄 Collecting {len(indicators)} indicators for category: {category}")
    
    for i, (series_id, name) in enumerate(indicators.items(), 1):
        logger.info(f"  [{i}/{len(indicators)}] {name} ({series_id})...")
        
        try:
            # Get series metadata
            metadata = collector.get_series_info(series_id)
            
            if not metadata:
                logger.warning(f"    ⚠️ No metadata found for {series_id}")
                batch_stats['failed'] += 1
                continue
            
            # Get series data
            data = collector.get_series_data(series_id, start_date=start_date)
            
            if data.empty:
                logger.warning(f"    ⚠️ No data found for {series_id}")
                batch_stats['failed'] += 1
                continue
            
            # Clean and validate data
            data['is_null'] = data['value'].isna().astype(int)
            data['value'] = pd.to_numeric(data['value'], errors='coerce')
            data['is_null'] = data['value'].isna().astype(int)
            
            cursor = conn.cursor()
            
            # Save indicator metadata
            cursor.execute('''
            INSERT OR REPLACE INTO indicators 
            (series_id, name, category, units, frequency, description, first_date, last_date, last_updated, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                series_id,
                name,
                category,
                metadata.get('units', ''),
                metadata.get('frequency', ''),
                metadata.get('notes', ''),
                data['date'].min().strftime('%Y-%m-%d') if not data.empty else None,
                data['date'].max().strftime('%Y-%m-%d') if not data.empty else None,
                datetime.now().isoformat(),
                str(metadata)
            ))
            
            # Save observations
            saved_count = 0
            null_count = 0
            
            for _, row in data.iterrows():
                try:
                    is_null = pd.isna(row['value'])
                    cursor.execute('''
                    INSERT OR REPLACE INTO observations
                    (series_id, date, value, is_null)
                    VALUES (?, ?, ?, ?)
                    ''', (
                        series_id,
                        row['date'].strftime('%Y-%m-%d'),
                        row['value'] if not is_null else None,
                        int(is_null)
                    ))
                    saved_count += 1
                    if is_null:
                        null_count += 1
                except Exception as e:
                    logger.warning(f"    ⚠️ Error saving observation: {e}")
            
            conn.commit()
            
            # Update statistics
            batch_stats['successful'] += 1
            batch_stats['total_observations'] += saved_count
            
            indicator_stats = {
                'series_id': series_id,
                'name': name,
                'observations': saved_count,
                'null_count': null_count,
                'completeness': ((saved_count - null_count) / saved_count * 100) if saved_count > 0 else 0
            }
            batch_stats['indicators'].append(indicator_stats)
            
            logger.info(f"    ✅ {saved_count} observations ({null_count} nulls)")
            
        except Exception as e:
            logger.error(f"    ❌ Error collecting {series_id}: {e}")
            batch_stats['failed'] += 1
    
    return batch_stats

def collect_comprehensive_data(db_path: str, start_date: str = '2010-01-01', 
                             categories: list = None):
    """
    Collect comprehensive macro economic data
    """
    
    # Initialize collector
    collector = FREDCollector()
    
    # Connect to database (use existing clean database)
    conn = sqlite3.connect(db_path)
    
    # Determine which categories to collect
    if categories is None:
        categories_to_collect = COMPREHENSIVE_INDICATORS.keys()
    else:
        categories_to_collect = [cat for cat in categories if cat in COMPREHENSIVE_INDICATORS]
    
    # Collection statistics
    overall_stats = {
        'start_time': datetime.now(),
        'categories': [],
        'total_indicators': 0,
        'successful_indicators': 0,
        'failed_indicators': 0,
        'total_observations': 0
    }
    
    logger.info(f"🚀 Starting comprehensive data collection...")
    logger.info(f"📊 Categories: {list(categories_to_collect)}")
    logger.info(f"📅 Start date: {start_date}")
    
    # Collect data by category
    for category in categories_to_collect:
        indicators = COMPREHENSIVE_INDICATORS[category]
        
        batch_stats = collect_indicator_batch(
            collector, indicators, category, conn, start_date
        )
        
        overall_stats['categories'].append(batch_stats)
        overall_stats['total_indicators'] += batch_stats['total']
        overall_stats['successful_indicators'] += batch_stats['successful']
        overall_stats['failed_indicators'] += batch_stats['failed']
        overall_stats['total_observations'] += batch_stats['total_observations']
        
        logger.info(f"✅ {category}: {batch_stats['successful']}/{batch_stats['total']} indicators, {batch_stats['total_observations']} observations")
    
    overall_stats['end_time'] = datetime.now()
    overall_stats['duration'] = (overall_stats['end_time'] - overall_stats['start_time']).total_seconds()
    
    conn.close()
    
    return overall_stats

def print_collection_summary(stats: dict):
    """Print comprehensive collection summary"""
    
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE DATA COLLECTION SUMMARY")
    print("=" * 80)
    
    # Overall statistics
    success_rate = (stats['successful_indicators'] / stats['total_indicators'] * 100) if stats['total_indicators'] > 0 else 0
    
    print(f"🎯 OVERALL RESULTS:")
    print(f"  📈 Total indicators attempted: {stats['total_indicators']}")
    print(f"  ✅ Successful: {stats['successful_indicators']}")
    print(f"  ❌ Failed: {stats['failed_indicators']}")
    print(f"  📊 Success rate: {success_rate:.1f}%")
    print(f"  📋 Total observations: {stats['total_observations']:,}")
    print(f"  ⏱️ Duration: {stats['duration']:.1f} seconds")
    
    # By category
    print(f"\n📋 BY CATEGORY:")
    for cat_stats in stats['categories']:
        cat_success_rate = (cat_stats['successful'] / cat_stats['total'] * 100) if cat_stats['total'] > 0 else 0
        print(f"  📊 {cat_stats['category']}: {cat_stats['successful']}/{cat_stats['total']} ({cat_success_rate:.1f}%) - {cat_stats['total_observations']:,} obs")
    
    # Top performers
    print(f"\n🏆 TOP PERFORMING CATEGORIES:")
    sorted_categories = sorted(stats['categories'], key=lambda x: x['successful'], reverse=True)
    for cat_stats in sorted_categories[:5]:
        print(f"  🥇 {cat_stats['category']}: {cat_stats['successful']} indicators, {cat_stats['total_observations']:,} observations")

def main():
    """Main function"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators_clean.db')
    
    print("🚀 COMPREHENSIVE MACRO DATA COLLECTION")
    print("=" * 80)
    print("📊 Expanding to full macro economic dataset...")
    print("🎯 Target: 100+ indicators across 12 categories")
    print("📅 Historical data: 2010-2025 (15+ years)")
    
    # Collect comprehensive data
    stats = collect_comprehensive_data(
        db_path=db_path,
        start_date='2010-01-01'
    )
    
    # Print summary
    print_collection_summary(stats)
    
    print(f"\n✅ COMPREHENSIVE COLLECTION COMPLETED!")
    print(f"📊 Database: {db_path}")
    print(f"🎯 Ready for advanced macro analysis!")

if __name__ == "__main__":
    main()
