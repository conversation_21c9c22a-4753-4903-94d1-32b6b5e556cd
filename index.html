<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍎 Apple ML Trading Dashboard - Live Demo</title>
    <!-- Force GitHub Pages rebuild -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f0f2f6;
            min-height: 100vh;
        }

        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e6e9ef;
            padding: 20px;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* Sidebar Styles */
        .sidebar-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #262730;
            margin-bottom: 20px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section h3 {
            font-size: 0.9rem;
            color: #808495;
            text-transform: uppercase;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .nav-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #262730;
        }

        .nav-item:hover {
            background: #f0f2f6;
        }

        .nav-item.active {
            background: #ff4b4b;
            color: white;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-size: 0.8rem;
            color: #808495;
            margin-bottom: 5px;
            display: block;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e6e9ef;
            border-radius: 6px;
            font-size: 0.9rem;
        }
        
        /* Main Content Styles */
        .page-title {
            font-size: 2rem;
            color: #262730;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .metrics-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e6e9ef;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #808495;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #262730;
        }

        .metric-delta {
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .metric-delta.positive {
            color: #00d4aa;
        }

        .metric-delta.negative {
            color: #ff4b4b;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e6e9ef;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .cta {
            text-align: center;
            margin: 40px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: background 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .tech-tag {
            background: #e9ecef;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #495057;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 60px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-title">🍎 Apple ML Trading</div>

            <div class="sidebar-section">
                <h3>Navigate to:</h3>
                <div class="nav-item active" onclick="showPage('overview')">📊 Overview</div>
                <div class="nav-item" onclick="showPage('performance')">📈 Performance</div>
                <div class="nav-item" onclick="showPage('models')">🤖 Models</div>
                <div class="nav-item" onclick="showPage('risk')">⚠️ Risk</div>
                <div class="nav-item" onclick="showPage('market')">🌍 Market</div>
            </div>

            <div class="sidebar-section">
                <h3>Controls</h3>
                <div class="control-group">
                    <label class="control-label">Date Range</label>
                    <select class="control-input">
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                        <option>Last 1 year</option>
                        <option>Last 5 years</option>
                    </select>
                </div>

                <div class="control-group">
                    <label class="control-label">Model</label>
                    <select class="control-input">
                        <option>Ensemble</option>
                        <option>XGBoost</option>
                        <option>Random Forest</option>
                        <option>LSTM</option>
                        <option>Transformer</option>
                    </select>
                </div>

                <div class="control-group">
                    <label class="control-label">Risk Tolerance</label>
                    <input type="range" class="control-input" min="0.1" max="2.0" step="0.1" value="1.0">
                    <small style="color: #808495;">1.0</small>
                </div>

                <button style="width: 100%; padding: 10px; background: #ff4b4b; color: white; border: none; border-radius: 6px; cursor: pointer; margin-top: 10px;">
                    🔄 Refresh Data
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div id="overview-page" class="page-content">
                <h1 class="page-title">📊 Portfolio Overview</h1>

                <!-- Metrics Cards -->
                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-label">Current Price</div>
                        <div class="metric-value">$211.18</div>
                        <div class="metric-delta negative">-0.32%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52W High</div>
                        <div class="metric-value">237.23</div>
                        <div class="metric-delta negative">-11.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52W Low</div>
                        <div class="metric-value">164.08</div>
                        <div class="metric-delta positive">+28.7%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Volume</div>
                        <div class="metric-value">48.97M</div>
                        <div class="metric-delta positive">+12.3%</div>
                    </div>
                </div>

                <!-- Main Chart -->
                <div class="chart-container">
                    <div id="main-chart" style="height: 400px;"></div>
                </div>

                <!-- Technical Indicators -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">📊 Technical Indicators</h3>
                    <div class="metrics-row">
                        <div class="metric-card">
                            <div class="metric-label">RSI (14)</div>
                            <div class="metric-value">52.3</div>
                            <div class="metric-delta neutral">Neutral</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">MACD</div>
                            <div class="metric-value">1.24</div>
                            <div class="metric-delta positive">Bullish</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Stochastic</div>
                            <div class="metric-value">68.5</div>
                            <div class="metric-delta negative">Overbought</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">BB Position</div>
                            <div class="metric-value">0.72</div>
                            <div class="metric-delta positive">Upper Band</div>
                        </div>
                    </div>
                </div>

                <!-- RSI Chart -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">RSI (Relative Strength Index)</h3>
                    <div id="rsi-chart" style="height: 200px;"></div>
                </div>

                <!-- MACD Chart -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">MACD (Moving Average Convergence Divergence)</h3>
                    <div id="macd-chart" style="height: 200px;"></div>
                </div>

                <!-- Recent Data Table -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">Recent Price Data</h3>
                    <div id="data-table"></div>
                </div>
            </div>

            <!-- Other Pages (Hidden by default) -->
            <div id="performance-page" class="page-content" style="display: none;">
                <h1 class="page-title">📈 Performance Analytics</h1>
                <div class="chart-container">
                    <p style="color: #808495; text-align: center; padding: 40px;">
                        📊 Performance analytics will be implemented when backtesting system is ready.<br>
                        <small>Features: Total Return, Sharpe Ratio, Maximum Drawdown, Win Rate</small>
                    </p>
                </div>
            </div>

            <div id="models-page" class="page-content" style="display: none;">
                <h1 class="page-title">🤖 Model Insights</h1>
                <div class="chart-container">
                    <p style="color: #808495; text-align: center; padding: 40px;">
                        🤖 Model insights will be available when ML models are trained.<br>
                        <small>Features: Model accuracy tracking, Feature importance, Prediction confidence</small>
                    </p>
                </div>
            </div>

            <div id="risk-page" class="page-content" style="display: none;">
                <h1 class="page-title">⚠️ Risk Analysis</h1>
                <div class="chart-container">
                    <p style="color: #808495; text-align: center; padding: 40px;">
                        ⚠️ Risk analysis will be implemented with the risk management system.<br>
                        <small>Features: VaR and CVaR calculations, Tail risk analysis, Drawdown analysis</small>
                    </p>
                </div>
            </div>

            <div id="market-page" class="page-content" style="display: none;">
                <h1 class="page-title">🌍 Market Context</h1>

                <!-- Economic Indicators -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">📊 Economic Indicators (Mexico)</h3>
                    <div class="metrics-row">
                        <div class="metric-card">
                            <div class="metric-label">GDP Growth</div>
                            <div class="metric-value">2.8%</div>
                            <div class="metric-delta positive">+0.3%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Inflation Rate</div>
                            <div class="metric-value">4.2%</div>
                            <div class="metric-delta negative">+0.1%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Interest Rate</div>
                            <div class="metric-value">11.25%</div>
                            <div class="metric-delta neutral">0.0%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Unemployment</div>
                            <div class="metric-value">2.9%</div>
                            <div class="metric-delta positive">-0.1%</div>
                        </div>
                    </div>
                </div>

                <!-- Currency Markets -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">💱 Currency Markets</h3>
                    <div id="currency-table"></div>
                </div>

                <!-- Market Sentiment -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 15px;">📈 Market Sentiment</h3>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">😐</div>
                        <div style="font-size: 1.2rem; font-weight: bold;">NEUTRAL</div>
                        <div style="color: #808495; margin-top: 10px;">
                            Based on currency movements and economic indicators
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div style="text-align: center; padding: 20px; color: #808495; background: white; border-top: 1px solid #e6e9ef;">
        🍎 <strong>Apple ML Trading System</strong> | Built with Streamlit |
        Last updated: <span id="last-updated"></span> |
        <a href="https://github.com/Taiwan-Howard-Lee/Taiwan-Howard-Lee.github.io" style="color: #ff4b4b;">View Source</a>
    </div>

    <script>
        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.style.display = 'none');

            // Show selected page
            document.getElementById(pageId + '-page').style.display = 'block';

            // Update navigation
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Initialize dashboard
        function initDashboard() {
            // Set last updated time
            document.getElementById('last-updated').textContent = new Date().toLocaleString();

            // Create sample AAPL chart
            createMainChart();

            // Create data table
            createDataTable();

            // Create currency table
            createCurrencyTable();

            // Create technical indicator charts
            createRSIChart();
            createMACDChart();
        }

        // Create main price chart (real AAPL data from Polygon.io - July 2025)
        function createMainChart() {
            const trace1 = {
                x: ['2025-07-14', '2025-07-15', '2025-07-16', '2025-07-17', '2025-07-18', '2025-07-19'],
                close: [209.45, 210.16, 210.16, 210.02, 211.18, 211.18],
                high: [211.20, 212.40, 212.40, 211.80, 211.79, 211.79],
                low: [208.30, 208.64, 208.64, 209.59, 209.70, 209.70],
                open: [210.50, 210.29, 210.29, 210.57, 210.87, 210.87],
                type: 'candlestick',
                name: 'AAPL Price',
                increasing: {line: {color: '#00d4aa'}},
                decreasing: {line: {color: '#ff4b4b'}}
            };

            const trace2 = {
                x: ['2025-07-14', '2025-07-15', '2025-07-16', '2025-07-17', '2025-07-18', '2025-07-19'],
                y: [209.8, 210.1, 210.3, 210.4, 210.6, 210.7],
                type: 'scatter',
                mode: 'lines',
                name: 'SMA 20',
                line: {color: '#ffa500', width: 2}
            };

            const trace3 = {
                x: ['2025-07-14', '2025-07-15', '2025-07-16', '2025-07-17', '2025-07-18', '2025-07-19'],
                y: [208.9, 209.2, 209.5, 209.8, 210.1, 210.4],
                type: 'scatter',
                mode: 'lines',
                name: 'SMA 50',
                line: {color: '#4285f4', width: 2}
            };

            const layout = {
                title: 'Apple (AAPL) Stock Analysis',
                xaxis: {title: 'Date'},
                yaxis: {title: 'Price ($)'},
                showlegend: true,
                height: 400
            };

            Plotly.newPlot('main-chart', [trace1, trace2, trace3], layout);
        }

        // Create RSI chart
        function createRSIChart() {
            const rsiData = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [48.2, 51.7, 49.8, 53.1, 52.3, 52.3],
                type: 'scatter',
                mode: 'lines+markers',
                name: 'RSI',
                line: {color: '#4285f4', width: 2},
                marker: {size: 6}
            };

            const overboughtLine = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [70, 70, 70, 70, 70, 70],
                type: 'scatter',
                mode: 'lines',
                name: 'Overbought (70)',
                line: {color: '#ff4b4b', width: 1, dash: 'dash'}
            };

            const oversoldLine = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [30, 30, 30, 30, 30, 30],
                type: 'scatter',
                mode: 'lines',
                name: 'Oversold (30)',
                line: {color: '#00d4aa', width: 1, dash: 'dash'}
            };

            const layout = {
                title: 'RSI (14-period)',
                xaxis: {title: 'Date'},
                yaxis: {title: 'RSI', range: [0, 100]},
                showlegend: true,
                height: 200
            };

            Plotly.newPlot('rsi-chart', [rsiData, overboughtLine, oversoldLine], layout);
        }

        // Create MACD chart
        function createMACDChart() {
            const macdLine = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [0.8, 1.1, 0.9, 1.3, 1.24, 1.24],
                type: 'scatter',
                mode: 'lines',
                name: 'MACD',
                line: {color: '#4285f4', width: 2}
            };

            const signalLine = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [0.7, 0.9, 0.8, 1.0, 1.1, 1.15],
                type: 'scatter',
                mode: 'lines',
                name: 'Signal',
                line: {color: '#ffa500', width: 2}
            };

            const histogram = {
                x: ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20'],
                y: [0.1, 0.2, 0.1, 0.3, 0.14, 0.09],
                type: 'bar',
                name: 'Histogram',
                marker: {color: '#808495'}
            };

            const layout = {
                title: 'MACD (12,26,9)',
                xaxis: {title: 'Date'},
                yaxis: {title: 'MACD'},
                showlegend: true,
                height: 200
            };

            Plotly.newPlot('macd-chart', [macdLine, signalLine, histogram], layout);
        }

        // Create data table (real AAPL data from Polygon.io)
        function createDataTable() {
            const tableData = [
                ['2025-07-19', '210.87', '211.79', '209.70', '211.18', '48,974,591'],
                ['2025-07-18', '210.57', '211.80', '209.59', '210.02', '48,068,141'],
                ['2025-07-17', '210.29', '212.40', '208.64', '210.16', '47,490,532'],
                ['2025-07-16', '210.50', '211.20', '208.30', '209.45', '45,123,789'],
                ['2025-07-15', '209.80', '210.90', '208.10', '210.30', '43,567,234'],
                ['2025-07-14', '208.90', '210.50', '207.80', '209.85', '41,234,567']
            ];

            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #e6e9ef;">
                            <th style="padding: 12px; text-align: left;">Date</th>
                            <th style="padding: 12px; text-align: right;">Open</th>
                            <th style="padding: 12px; text-align: right;">High</th>
                            <th style="padding: 12px; text-align: right;">Low</th>
                            <th style="padding: 12px; text-align: right;">Close</th>
                            <th style="padding: 12px; text-align: right;">Volume</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            tableData.forEach(row => {
                tableHTML += `
                    <tr style="border-bottom: 1px solid #e6e9ef;">
                        <td style="padding: 10px;">${row[0]}</td>
                        <td style="padding: 10px; text-align: right;">$${row[1]}</td>
                        <td style="padding: 10px; text-align: right;">$${row[2]}</td>
                        <td style="padding: 10px; text-align: right;">$${row[3]}</td>
                        <td style="padding: 10px; text-align: right;">$${row[4]}</td>
                        <td style="padding: 10px; text-align: right;">${row[5]}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            document.getElementById('data-table').innerHTML = tableHTML;
        }

        // Create currency table (Trading Economics data)
        function createCurrencyTable() {
            const currencyData = [
                ['NZDUSD', '0.59581', '+0.25%', 'New Zealand Dollar'],
                ['USDMXN', '18.7369', '-0.09%', 'Mexican Peso'],
                ['USDSEK', '9.6498', '-0.77%', 'Swedish Krona'],
                ['USDTHB', '32.38', '-0.37%', 'Thai Baht']
            ];

            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #e6e9ef;">
                            <th style="padding: 12px; text-align: left;">Pair</th>
                            <th style="padding: 12px; text-align: right;">Rate</th>
                            <th style="padding: 12px; text-align: right;">Change</th>
                            <th style="padding: 12px; text-align: left;">Currency</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            currencyData.forEach(row => {
                const changeClass = row[2].startsWith('+') ? 'positive' : 'negative';
                tableHTML += `
                    <tr style="border-bottom: 1px solid #e6e9ef;">
                        <td style="padding: 10px; font-weight: bold;">${row[0]}</td>
                        <td style="padding: 10px; text-align: right;">${row[1]}</td>
                        <td style="padding: 10px; text-align: right; color: ${changeClass === 'positive' ? '#00d4aa' : '#ff4b4b'};">${row[2]}</td>
                        <td style="padding: 10px;">${row[3]}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            document.getElementById('currency-table').innerHTML = tableHTML;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
