#!/usr/bin/env python3
"""
Fix Data Quality Issues - Clean Database and Proper Collection

This script addresses all the critical data quality problems:
1. Clean database schema
2. Proper null value handling
3. Correct data types and constraints
4. Proper metadata storage
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.collectors.fred_collector import FREDCollector

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_clean_database(db_path: str):
    """Create a clean database with proper schema"""
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
        logger.info(f"Removed existing database: {db_path}")
    
    # Create directory if needed
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Create new database with clean schema
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create indicators table
    cursor.execute('''
    CREATE TABLE indicators (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        category TEXT,
        units TEXT,
        frequency TEXT,
        description TEXT,
        source TEXT DEFAULT 'FRED',
        first_date TEXT,
        last_date TEXT,
        last_updated TEXT,
        metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create observations table
    cursor.execute('''
    CREATE TABLE observations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id TEXT NOT NULL,
        date TEXT NOT NULL,
        value REAL,
        is_null INTEGER DEFAULT 0,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(series_id, date),
        FOREIGN KEY (series_id) REFERENCES indicators (series_id)
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX idx_observations_series_date ON observations(series_id, date)')
    cursor.execute('CREATE INDEX idx_observations_date ON observations(date)')
    cursor.execute('CREATE INDEX idx_indicators_category ON indicators(category)')
    
    conn.commit()
    conn.close()
    
    logger.info(f"Created clean database: {db_path}")

def collect_indicator_with_quality_checks(collector: FREDCollector, series_id: str, name: str, 
                                        category: str, conn: sqlite3.Connection, 
                                        start_date: str = '2010-01-01') -> dict:
    """
    Collect a single indicator with proper quality checks and error handling
    """
    
    stats = {
        'series_id': series_id,
        'name': name,
        'success': False,
        'observations': 0,
        'null_values': 0,
        'error': None
    }
    
    try:
        # Get series metadata
        logger.info(f"Getting metadata for {series_id}...")
        metadata = collector.get_series_info(series_id)
        
        if not metadata:
            stats['error'] = 'No metadata found'
            return stats
        
        # Get series data
        logger.info(f"Getting data for {series_id}...")
        data = collector.get_series_data(series_id, start_date=start_date)
        
        if data.empty:
            stats['error'] = 'No data returned'
            return stats
        
        # Clean and validate data
        logger.info(f"Processing {len(data)} observations for {series_id}...")
        
        # Handle null values properly
        data['is_null'] = data['value'].isna().astype(int)
        null_count = data['is_null'].sum()
        
        # Convert '.' to NaN for FRED data
        data['value'] = pd.to_numeric(data['value'], errors='coerce')
        data['is_null'] = data['value'].isna().astype(int)
        
        cursor = conn.cursor()
        
        # Save indicator metadata
        cursor.execute('''
        INSERT OR REPLACE INTO indicators 
        (series_id, name, category, units, frequency, description, first_date, last_date, last_updated, metadata)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            series_id,
            name,
            category,
            metadata.get('units', ''),
            metadata.get('frequency', ''),
            metadata.get('notes', ''),
            data['date'].min().strftime('%Y-%m-%d') if not data.empty else None,
            data['date'].max().strftime('%Y-%m-%d') if not data.empty else None,
            datetime.now().isoformat(),
            str(metadata)
        ))
        
        # Save observations
        saved_count = 0
        for _, row in data.iterrows():
            try:
                cursor.execute('''
                INSERT OR REPLACE INTO observations
                (series_id, date, value, is_null)
                VALUES (?, ?, ?, ?)
                ''', (
                    series_id,
                    row['date'].strftime('%Y-%m-%d'),
                    row['value'] if not pd.isna(row['value']) else None,
                    int(pd.isna(row['value']))
                ))
                saved_count += 1
            except Exception as e:
                logger.warning(f"Error saving observation for {series_id} on {row['date']}: {e}")
        
        conn.commit()
        
        stats['success'] = True
        stats['observations'] = saved_count
        stats['null_values'] = null_count
        
        logger.info(f"✅ Successfully saved {saved_count} observations for {name} ({series_id})")
        if null_count > 0:
            logger.info(f"   📊 {null_count} null values properly handled")
        
    except Exception as e:
        stats['error'] = str(e)
        logger.error(f"❌ Error collecting {series_id}: {e}")
    
    return stats

def collect_key_indicators(db_path: str, start_date: str = '2010-01-01'):
    """
    Collect key economic indicators with proper quality control
    """
    
    # Key indicators that should have good data quality
    key_indicators = {
        # Interest Rates (usually complete)
        'FEDFUNDS': ('Federal Funds Rate', 'interest_rates'),
        'DGS10': ('10-Year Treasury Rate', 'interest_rates'),
        'DGS2': ('2-Year Treasury Rate', 'interest_rates'),
        
        # Economic Growth (quarterly, usually complete)
        'GDP': ('Gross Domestic Product', 'economic_growth'),
        'GDPC1': ('Real GDP', 'economic_growth'),
        
        # Employment (monthly, usually complete)
        'UNRATE': ('Unemployment Rate', 'employment'),
        'PAYEMS': ('Total Nonfarm Payrolls', 'employment'),
        
        # Inflation (monthly, usually complete)
        'CPIAUCSL': ('Consumer Price Index', 'inflation'),
        'CPILFESL': ('Core CPI', 'inflation'),
        
        # Consumer (monthly, usually complete)
        'PCE': ('Personal Consumption Expenditures', 'consumer'),
        'UMCSENT': ('Consumer Sentiment', 'consumer'),
        
        # Housing (monthly, usually complete)
        'HOUST': ('Housing Starts', 'housing'),
        
        # Business (monthly, usually complete)
        'INDPRO': ('Industrial Production', 'business'),
        'NAPM': ('ISM Manufacturing PMI', 'business'),
        
        # Financial Markets
        'SP500': ('S&P 500', 'financial_markets'),
        'VIXCLS': ('VIX Volatility Index', 'financial_markets'),
    }
    
    # Initialize collector
    collector = FREDCollector()
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # Collection statistics
    results = []
    successful = 0
    failed = 0
    total_observations = 0
    
    logger.info(f"Starting collection of {len(key_indicators)} key indicators...")
    
    for i, (series_id, (name, category)) in enumerate(key_indicators.items(), 1):
        logger.info(f"[{i}/{len(key_indicators)}] Collecting {name} ({series_id})...")
        
        stats = collect_indicator_with_quality_checks(
            collector, series_id, name, category, conn, start_date
        )
        
        results.append(stats)
        
        if stats['success']:
            successful += 1
            total_observations += stats['observations']
        else:
            failed += 1
            logger.error(f"❌ Failed to collect {name}: {stats['error']}")
    
    conn.close()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 DATA QUALITY COLLECTION SUMMARY")
    print("=" * 60)
    print(f"✅ Successful: {successful}/{len(key_indicators)} indicators")
    print(f"❌ Failed: {failed}/{len(key_indicators)} indicators")
    print(f"📊 Total observations: {total_observations}")
    print(f"📈 Success rate: {(successful/len(key_indicators)*100):.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        obs_info = f"{result['observations']} obs" if result['success'] else result['error']
        null_info = f" ({result['null_values']} nulls)" if result['success'] and result['null_values'] > 0 else ""
        print(f"  {status} {result['name']}: {obs_info}{null_info}")
    
    return results

def verify_data_quality(db_path: str):
    """
    Verify the data quality after collection
    """
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("\n" + "=" * 60)
    print("🔍 DATA QUALITY VERIFICATION")
    print("=" * 60)
    
    # Check indicators table
    cursor.execute("SELECT COUNT(*) FROM indicators")
    indicator_count = cursor.fetchone()[0]
    print(f"📊 Indicators in database: {indicator_count}")
    
    # Check observations table
    cursor.execute("SELECT COUNT(*) FROM observations")
    observation_count = cursor.fetchone()[0]
    print(f"📈 Total observations: {observation_count}")
    
    # Check for null values
    cursor.execute("SELECT COUNT(*) FROM observations WHERE is_null = 1")
    null_count = cursor.fetchone()[0]
    print(f"🔍 Null values: {null_count} ({null_count/observation_count*100:.1f}%)")
    
    # Check data by indicator
    cursor.execute('''
    SELECT i.name, i.series_id, COUNT(o.id) as obs_count, 
           SUM(o.is_null) as null_count,
           MIN(o.date) as first_date,
           MAX(o.date) as last_date
    FROM indicators i
    LEFT JOIN observations o ON i.series_id = o.series_id
    GROUP BY i.series_id, i.name
    ORDER BY obs_count DESC
    ''')
    
    results = cursor.fetchall()
    
    print(f"\n📋 INDICATOR DETAILS:")
    for name, series_id, obs_count, null_count, first_date, last_date in results:
        null_pct = (null_count/obs_count*100) if obs_count > 0 else 0
        print(f"  📊 {name} ({series_id}): {obs_count} obs, {null_count} nulls ({null_pct:.1f}%), {first_date} to {last_date}")
    
    conn.close()

def main():
    """Main function to fix data quality issues"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators_clean.db')
    
    print("🚀 FIXING DATA QUALITY ISSUES")
    print("=" * 60)
    
    # Step 1: Create clean database
    print("1️⃣ Creating clean database schema...")
    create_clean_database(db_path)
    
    # Step 2: Collect key indicators with quality checks
    print("\n2️⃣ Collecting key indicators with quality control...")
    results = collect_key_indicators(db_path, start_date='2015-01-01')
    
    # Step 3: Verify data quality
    print("\n3️⃣ Verifying data quality...")
    verify_data_quality(db_path)
    
    print(f"\n✅ Clean database created: {db_path}")
    print("🎯 Ready for high-quality analysis!")

if __name__ == "__main__":
    main()
