"""
Comprehensive Registry of US Macro Economic Indicators

This module contains a comprehensive list of US economic indicators
organized by category with their FRED series IDs.
"""

from typing import Dict, List
from src.schema.data_schema import MacroIndicator

# Comprehensive US Economic Indicators Registry
US_MACRO_INDICATORS = {
    
    # ========== INTEREST RATES & MONETARY POLICY ==========
    'interest_rates': {
        'Federal Funds Rate': 'FEDFUNDS',
        'Federal Funds Upper Target': 'DFEDTARU',
        'Federal Funds Lower Target': 'DFEDTARL',
        '3-Month Treasury Rate': 'DGS3MO',
        '6-Month Treasury Rate': 'DGS6MO',
        '1-Year Treasury Rate': 'DGS1',
        '2-Year Treasury Rate': 'DGS2',
        '3-Year Treasury Rate': 'DGS3',
        '5-Year Treasury Rate': 'DGS5',
        '7-Year Treasury Rate': 'DGS7',
        '10-Year Treasury Rate': 'DGS10',
        '20-Year Treasury Rate': 'DGS20',
        '30-Year Treasury Rate': 'DGS30',
        'Yield Curve Spread (10Y-2Y)': 'T10Y2Y',
        'Yield Curve Spread (10Y-3M)': 'T10Y3M',
        'Real Interest Rate (10Y)': 'REAINTRATREARAT10Y',
        'Prime Loan Rate': 'DPRIME',
        'Bank Prime Loan Rate': 'MPRIME',
        'AAA Corporate Bond Yield': 'AAA',
        'BAA Corporate Bond Yield': 'BAA',
        'High Yield Credit Spread': 'BAMLH0A0HYM2',
        'Investment Grade Credit Spread': 'BAMLC0A0CM',
    },
    
    # ========== ECONOMIC GROWTH & ACTIVITY ==========
    'economic_growth': {
        'Gross Domestic Product': 'GDP',
        'Real GDP': 'GDPC1',
        'GDP Growth Rate (QoQ)': 'A191RL1Q225SBEA',
        'GDP Growth Rate (YoY)': 'A191RL1A225NBEA',
        'GDP per Capita': 'A939RX0Q048SBEA',
        'Real GDP per Capita': 'GDPCA',
        'Gross National Product': 'GNP',
        'Gross Domestic Income': 'GDI',
        'Industrial Production Index': 'INDPRO',
        'Manufacturing Production': 'IPMAN',
        'Capacity Utilization': 'TCU',
        'Manufacturing Capacity Utilization': 'MCUMFN',
        'ISM Manufacturing PMI': 'NAPM',
        'ISM Services PMI': 'NAPMSII',
        'Philadelphia Fed Index': 'PHILLY',
        'New York Fed Empire State Index': 'GACDINA066MNFRBNY',
        'Chicago Fed National Activity Index': 'CFNAI',
        'Leading Economic Index': 'USSLIND',
        'Coincident Economic Index': 'USALOLITONOSTSAM',
    },
    
    # ========== EMPLOYMENT & LABOR MARKET ==========
    'employment': {
        'Unemployment Rate': 'UNRATE',
        'Civilian Labor Force': 'CLF16OV',
        'Labor Force Participation Rate': 'CIVPART',
        'Employment-Population Ratio': 'EMRATIO',
        'Total Nonfarm Payrolls': 'PAYEMS',
        'Private Nonfarm Payrolls': 'USPRIV',
        'Government Employment': 'USGOVT',
        'Manufacturing Employment': 'MANEMP',
        'Construction Employment': 'USCONS',
        'Retail Trade Employment': 'USTRADE',
        'Professional Services Employment': 'USPBS',
        'Financial Activities Employment': 'USFIRE',
        'Initial Jobless Claims': 'ICSA',
        'Continuing Jobless Claims': 'CCSA',
        'Job Openings': 'JTSJOL',
        'Quits Rate': 'JTSQUR',
        'Hires Rate': 'JTSHIR',
        'Average Hourly Earnings': 'AHETPI',
        'Average Weekly Hours': 'AWHAETP',
        'Unit Labor Costs': 'ULCNFB',
        'Productivity': 'OPHNFB',
    },
    
    # ========== INFLATION & PRICES ==========
    'inflation': {
        'Consumer Price Index': 'CPIAUCSL',
        'Core CPI': 'CPILFESL',
        'CPI Year-over-Year': 'CPIAUCSL',
        'Core CPI Year-over-Year': 'CPILFESL',
        'Producer Price Index': 'PPIACO',
        'Core PPI': 'PPILFE',
        'PCE Price Index': 'PCEPI',
        'Core PCE Price Index': 'PCEPILFE',
        'PCE Year-over-Year': 'PCEPI',
        'Core PCE Year-over-Year': 'PCEPILFE',
        'Import Price Index': 'IR',
        'Export Price Index': 'IQ',
        'GDP Deflator': 'GDPDEF',
        'Employment Cost Index': 'ECIALLCIV',
        '5-Year Breakeven Inflation': 'T5YIE',
        '10-Year Breakeven Inflation': 'T10YIE',
        'University of Michigan Inflation Expectations': 'MICH',
    },
    
    # ========== CONSUMER SPENDING & CONFIDENCE ==========
    'consumer': {
        'Personal Consumption Expenditures': 'PCE',
        'Real Personal Consumption': 'PCEC1',
        'Personal Income': 'PI',
        'Real Personal Income': 'RPI',
        'Personal Saving Rate': 'PSAVERT',
        'Disposable Personal Income': 'DSPI',
        'Real Disposable Personal Income': 'DSPIC96',
        'Retail Sales': 'RSAFS',
        'Retail Sales Ex Auto': 'RSAFSNA',
        'Motor Vehicle Sales': 'TOTALSA',
        'E-commerce Sales': 'ECOMSA',
        'Consumer Confidence': 'UMCSENT',
        'Consumer Expectations': 'UMCSENT1',
        'Consumer Credit': 'TOTALSL',
        'Credit Card Delinquency Rate': 'DRCCLACBS',
        'Household Debt Service Ratio': 'TDSP',
    },
    
    # ========== HOUSING & CONSTRUCTION ==========
    'housing': {
        'Housing Starts': 'HOUST',
        'Housing Starts (Single Family)': 'HOUST1F',
        'Building Permits': 'PERMIT',
        'New Home Sales': 'HSN1F',
        'Existing Home Sales': 'EXHOSLUSM495S',
        'Median Home Price': 'MSPUS',
        'Case-Shiller Home Price Index': 'CSUSHPINSA',
        'Home Ownership Rate': 'RHORUSQ156N',
        'Housing Inventory': 'MSACSR',
        'Mortgage Rates (30Y Fixed)': 'MORTGAGE30US',
        'Mortgage Rates (15Y Fixed)': 'MORTGAGE15US',
        'Mortgage Applications': 'MBAVOL',
        'Construction Spending': 'TTLCONS',
        'Residential Construction': 'TLRESCONS',
        'Commercial Construction': 'TLCOMCONS',
    },
    
    # ========== BUSINESS ACTIVITY & SENTIMENT ==========
    'business': {
        'Business Inventories': 'BUSINV',
        'Manufacturing Inventories': 'MNFCTI',
        'Retail Inventories': 'RETAILIMSA',
        'Wholesale Inventories': 'WHLSLRIMSA',
        'Durable Goods Orders': 'DGORDER',
        'Core Durable Goods Orders': 'NEWORDER',
        'Factory Orders': 'AMTMNO',
        'Capital Goods Orders': 'CAPUTLB50001SQ',
        'Business Investment': 'PNFI',
        'Equipment Investment': 'Y033RC1Q027SBEA',
        'Structures Investment': 'Y001RC1Q027SBEA',
        'Intellectual Property Investment': 'Y020RC1Q027SBEA',
        'Corporate Profits': 'CP',
        'Corporate Profits After Tax': 'CPATAX',
        'Small Business Optimism': 'OPEM',
        'CEO Confidence': 'CONCEO',
    },
    
    # ========== TRADE & INTERNATIONAL ==========
    'trade': {
        'Trade Balance': 'BOPGSTB',
        'Exports of Goods and Services': 'EXPGS',
        'Imports of Goods and Services': 'IMPGS',
        'Goods Exports': 'EXPGSC1',
        'Goods Imports': 'IMPGSC1',
        'Services Exports': 'EXPSVC',
        'Services Imports': 'IMPSVC',
        'Current Account Balance': 'NETFI',
        'US Dollar Index': 'DTWEXBGS',
        'Real Trade Weighted Dollar': 'DTWEXM',
        'EUR/USD Exchange Rate': 'DEXUSEU',
        'GBP/USD Exchange Rate': 'DEXUSUK',
        'USD/JPY Exchange Rate': 'DEXJPUS',
        'USD/CAD Exchange Rate': 'DEXCAUS',
        'USD/CNY Exchange Rate': 'DEXCHUS',
        'Foreign Exchange Reserves': 'TRESEGUSM052N',
    },
    
    # ========== GOVERNMENT & FISCAL POLICY ==========
    'government': {
        'Federal Debt': 'GFDEBTN',
        'Federal Debt to GDP': 'GFDEGDQ188S',
        'Federal Surplus/Deficit': 'FYFSGDA188S',
        'Federal Receipts': 'FGRECPT',
        'Federal Outlays': 'FGEXPND',
        'Government Consumption': 'GCE',
        'Government Investment': 'GPDIC1',
        'State and Local Debt': 'SLGSDODNS',
        'Tax Receipts': 'W006RC1Q027SBEA',
        'Corporate Tax Receipts': 'W008RC1Q027SBEA',
        'Individual Tax Receipts': 'W002RC1Q027SBEA',
    },
    
    # ========== FINANCIAL MARKETS ==========
    'financial_markets': {
        'S&P 500': 'SP500',
        'NASDAQ Composite': 'NASDAQCOM',
        'Dow Jones Industrial Average': 'DJIA',
        'Russell 2000': 'RU2000PR',
        'VIX Volatility Index': 'VIXCLS',
        'CBOE Put/Call Ratio': 'PCCE',
        'NYSE Margin Debt': 'NYSEMTD',
        'Commercial Paper Outstanding': 'COMPOUT',
        'TED Spread': 'TEDRATE',
        'LIBOR-OIS Spread': 'USDONTD156N',
        'High Yield Bond Spread': 'BAMLH0A0HYM2',
        'Investment Grade Bond Spread': 'BAMLC0A0CM',
        'Term Premium (10Y)': 'THREEFYTP10',
    },
    
    # ========== BANKING & CREDIT ==========
    'banking': {
        'Total Bank Credit': 'TOTBKCR',
        'Commercial Bank Assets': 'TLAACBW027SBOG',
        'Bank Lending Standards': 'DRTSCILM',
        'Commercial & Industrial Loans': 'BUSLOANS',
        'Real Estate Loans': 'REALLN',
        'Consumer Loans': 'CONSUMER',
        'Credit Card Loans': 'CCLACBW027SBOG',
        'Auto Loans': 'MVLOAS',
        'Student Loans': 'SLOAS',
        'Bank Reserves': 'TOTRESNS',
        'Required Reserves': 'REQRESNS',
        'Excess Reserves': 'EXCSRESNS',
        'M1 Money Supply': 'M1SL',
        'M2 Money Supply': 'M2SL',
        'Velocity of M2': 'M2V',
    },
    
    # ========== COMMODITIES & RESOURCES ==========
    'commodities': {
        'WTI Crude Oil': 'DCOILWTICO',
        'Brent Crude Oil': 'DCOILBRENTEU',
        'Natural Gas': 'DHHNGSP',
        'Gold Price': 'GOLDAMGBD228NLBM',
        'Silver Price': 'SLVPRUSD',
        'Copper Price': 'PCOPPUSDM',
        'Aluminum Price': 'PALUMUSDM',
        'Corn Price': 'PMAIZMTUSDM',
        'Wheat Price': 'PWHEAMTUSDM',
        'Soybeans Price': 'PSOYBUSDM',
        'Coffee Price': 'PCOFFOTMUSDM',
        'Cotton Price': 'PCOTTINDUSDM',
        'Baltic Dry Index': 'BDIY',
        'CRB Commodity Index': 'DJCI',
        'DJP Commodity ETF': 'DJPSP',
    },
}

def get_all_indicators() -> Dict[str, str]:
    """
    Get a flat dictionary of all indicators across all categories.
    
    Returns:
        Dictionary mapping indicator names to FRED series IDs
    """
    all_indicators = {}
    for category, indicators in US_MACRO_INDICATORS.items():
        all_indicators.update(indicators)
    return all_indicators

def get_indicators_by_category(category: str) -> Dict[str, str]:
    """
    Get indicators for a specific category.
    
    Args:
        category: Category name
        
    Returns:
        Dictionary mapping indicator names to FRED series IDs for the category
    """
    return US_MACRO_INDICATORS.get(category, {})

def get_category_list() -> List[str]:
    """
    Get list of all available categories.
    
    Returns:
        List of category names
    """
    return list(US_MACRO_INDICATORS.keys())

def get_indicator_count() -> Dict[str, int]:
    """
    Get count of indicators per category.
    
    Returns:
        Dictionary mapping category names to indicator counts
    """
    return {category: len(indicators) for category, indicators in US_MACRO_INDICATORS.items()}

def get_total_indicator_count() -> int:
    """
    Get total number of indicators across all categories.
    
    Returns:
        Total number of indicators
    """
    return sum(len(indicators) for indicators in US_MACRO_INDICATORS.values())

# Summary statistics
REGISTRY_SUMMARY = {
    'total_indicators': get_total_indicator_count(),
    'categories': len(US_MACRO_INDICATORS),
    'indicators_per_category': get_indicator_count(),
}
