# Macro Economic Indicators Project Requirements

# Core Data Processing
pandas>=2.0.0
numpy>=1.24.0
python-dateutil>=2.8.2

# Data Collection APIs
tradingeconomics>=4.5.0
yfinance>=0.2.0
fredapi>=0.5.0
alpha-vantage>=2.3.0

# Data Storage
sqlite3  # Built-in with Python
sqlalchemy>=2.0.0

# Analysis & Modeling
scikit-learn>=1.3.0
statsmodels>=0.14.0
scipy>=1.10.0

# Visualization
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0
streamlit>=1.25.0

# Utilities
requests>=2.31.0
python-dotenv>=1.0.0
schedule>=1.2.0
logging

# Development
jupyter>=1.0.0
pytest>=7.4.0
