#!/usr/bin/env python3
"""
Test script for FRED API (Federal Reserve Economic Data)
This is a free API that provides comprehensive US economic data
"""

import os
import pandas as pd
from dotenv import load_dotenv
import requests
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# FRED API base URL
FRED_API_BASE_URL = "https://api.stlouisfed.org/fred/series"

def get_fred_api_key():
    """Get FRED API key from environment or prompt user"""
    api_key = os.getenv('FRED_API_KEY')
    
    if not api_key:
        print("⚠️ FRED API key not found in environment variables.")
        print("You can get a free API key at: https://fred.stlouisfed.org/docs/api/api_key.html")
        api_key = input("Enter your FRED API key (or press Enter to skip): ").strip()
        
        if api_key:
            # Save to .env file for future use
            with open('.env', 'a') as f:
                f.write(f"\nFRED_API_KEY={api_key}")
            print("✅ API key saved to .env file")
    
    return api_key

def get_fred_series(series_id, api_key, observation_start=None, observation_end=None):
    """Get data for a specific FRED series"""
    
    # Set default dates if not provided
    if observation_end is None:
        observation_end = datetime.now().strftime('%Y-%m-%d')
    
    if observation_start is None:
        # Default to 10 years of data
        start_date = datetime.now() - timedelta(days=365*10)
        observation_start = start_date.strftime('%Y-%m-%d')
    
    # Build request URL
    url = f"{FRED_API_BASE_URL}/observations"
    
    params = {
        'series_id': series_id,
        'api_key': api_key,
        'file_type': 'json',
        'observation_start': observation_start,
        'observation_end': observation_end,
        'sort_order': 'desc',
        'units': 'lin'  # Linear units (no transformation)
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        data = response.json()
        
        if 'observations' in data:
            # Convert to DataFrame
            df = pd.DataFrame(data['observations'])
            # Convert date strings to datetime
            df['date'] = pd.to_datetime(df['date'])
            # Convert values to float where possible
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            
            return df
        else:
            print(f"⚠️ No observations found for series {series_id}")
            return None
            
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP Error: {e}")
        if response.status_code == 400:
            print(f"Response content: {response.text}")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def get_fred_series_info(series_id, api_key):
    """Get metadata for a specific FRED series"""
    
    url = f"{FRED_API_BASE_URL}"
    
    params = {
        'series_id': series_id,
        'api_key': api_key,
        'file_type': 'json'
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if 'seriess' in data and len(data['seriess']) > 0:
            return data['seriess'][0]
        else:
            print(f"⚠️ No information found for series {series_id}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting series info: {str(e)}")
        return None

def test_us_economic_indicators():
    """Test retrieving key US economic indicators from FRED"""
    
    api_key = get_fred_api_key()
    
    if not api_key:
        print("❌ Cannot proceed without FRED API key")
        return
    
    # List of key US economic indicators with their FRED series IDs
    indicators = {
        'GDP': 'GDP',                      # Gross Domestic Product
        'UNRATE': 'UNRATE',                # Unemployment Rate
        'CPIAUCSL': 'CPIAUCSL',            # Consumer Price Index (CPI)
        'FEDFUNDS': 'FEDFUNDS',            # Federal Funds Rate
        'DGS10': 'DGS10',                  # 10-Year Treasury Yield
        'INDPRO': 'INDPRO',                # Industrial Production Index
        'RSAFS': 'RSAFS',                  # Retail Sales
        'HOUST': 'HOUST',                  # Housing Starts
        'UMCSENT': 'UMCSENT',              # Consumer Sentiment
        'PAYEMS': 'PAYEMS'                 # Nonfarm Payrolls
    }
    
    print("\n🇺🇸 Testing US Economic Indicators from FRED")
    print("=" * 50)
    
    for name, series_id in indicators.items():
        print(f"\n📊 Testing {name} (Series ID: {series_id})...")
        
        # Get series info
        series_info = get_fred_series_info(series_id, api_key)
        
        if series_info:
            print(f"✅ Series info retrieved:")
            print(f"  Title: {series_info.get('title', 'N/A')}")
            print(f"  Units: {series_info.get('units', 'N/A')}")
            print(f"  Frequency: {series_info.get('frequency', 'N/A')}")
            
            # Get series data
            data = get_fred_series(series_id, api_key)
            
            if data is not None and not data.empty:
                print(f"✅ Data retrieved: {len(data)} observations")
                print(f"  Latest data ({data['date'].iloc[0]}): {data['value'].iloc[0]}")
                
                # Show a few recent values
                print(f"  Recent values:")
                for i, row in data.head(5).iterrows():
                    print(f"    {row['date'].strftime('%Y-%m-%d')}: {row['value']}")
            else:
                print("⚠️ No data retrieved")
        else:
            print(f"⚠️ Could not retrieve series info")
    
    print("\n" + "=" * 50)
    print("🎉 FRED API test completed!")

if __name__ == "__main__":
    print("🧪 Testing FRED API for US Economic Data")
    print("=" * 50)
    
    test_us_economic_indicators()
