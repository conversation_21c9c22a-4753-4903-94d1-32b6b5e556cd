#!/usr/bin/env python3
"""
EXHAUST FRED API WITH KEY ROTATION - Collect EVERYTHING Available

This script uses multiple API keys with intelligent rotation to maximize
data collection and avoid rate limits.
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
import time
import json
import random
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.collectors.fred_collector import FREDCollector

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RotatingFREDCollector:
    """
    FRED Collector with API key rotation for maximum throughput
    """
    
    def __init__(self):
        # Multiple API keys for rotation
        self.api_keys = [
            '82142eab0beeffeb447d52704d34cbff',  # Original key
            'a47f9fbb6311c2da5b191e71106130f7',  # Key 2
            '59741bef57a7f1b48ce280a4ddbde77b'   # Key 3
        ]
        
        # Initialize collectors for each key
        self.collectors = {}
        self.key_stats = {}
        
        for i, api_key in enumerate(self.api_keys):
            try:
                collector = FREDCollector(api_key=api_key)
                self.collectors[api_key] = collector
                self.key_stats[api_key] = {
                    'requests': 0,
                    'successes': 0,
                    'failures': 0,
                    'rate_limits': 0,
                    'last_used': None,
                    'cooldown_until': None
                }
                logger.info(f"✅ Initialized collector {i+1}/3 with key: {api_key[:10]}...")
            except Exception as e:
                logger.error(f"❌ Failed to initialize collector {i+1}: {e}")
        
        self.current_key_index = 0
        logger.info(f"🔄 Rotation system ready with {len(self.collectors)} API keys")
    
    def get_next_available_key(self):
        """Get the next available API key, considering cooldowns"""
        
        current_time = datetime.now()
        
        # Check if current key is available
        for _ in range(len(self.api_keys)):
            key = self.api_keys[self.current_key_index]
            stats = self.key_stats[key]
            
            # Check if key is in cooldown
            if stats['cooldown_until'] and current_time < stats['cooldown_until']:
                cooldown_remaining = (stats['cooldown_until'] - current_time).total_seconds()
                logger.debug(f"🔄 Key {key[:10]}... in cooldown for {cooldown_remaining:.1f}s")
                self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
                continue
            
            # Key is available
            return key
        
        # All keys in cooldown - wait for the shortest one
        min_cooldown_key = min(
            self.api_keys,
            key=lambda k: self.key_stats[k]['cooldown_until'] or datetime.min
        )
        
        if self.key_stats[min_cooldown_key]['cooldown_until']:
            wait_time = (self.key_stats[min_cooldown_key]['cooldown_until'] - current_time).total_seconds()
            if wait_time > 0:
                logger.info(f"⏸️ All keys in cooldown, waiting {wait_time:.1f}s for key {min_cooldown_key[:10]}...")
                time.sleep(wait_time + 1)
        
        return min_cooldown_key
    
    def collect_with_rotation(self, series_id: str, name: str, category: str, 
                            conn: sqlite3.Connection, start_date: str = '2000-01-01') -> dict:
        """
        Collect indicator with intelligent API key rotation
        """
        
        stats = {
            'series_id': series_id,
            'name': name,
            'category': category,
            'success': False,
            'observations': 0,
            'null_values': 0,
            'error': None,
            'api_key_used': None,
            'attempts': 0
        }
        
        max_attempts = len(self.api_keys) * 2  # Try each key twice
        
        for attempt in range(max_attempts):
            try:
                # Get next available API key
                api_key = self.get_next_available_key()
                collector = self.collectors[api_key]
                
                stats['api_key_used'] = api_key[:10] + '...'
                stats['attempts'] = attempt + 1
                
                # Update key stats
                self.key_stats[api_key]['requests'] += 1
                self.key_stats[api_key]['last_used'] = datetime.now()
                
                # Get series metadata
                metadata = collector.get_series_info(series_id)
                
                if not metadata:
                    stats['error'] = 'No metadata found'
                    self.key_stats[api_key]['failures'] += 1
                    continue
                
                # Get series data
                data = collector.get_series_data(series_id, start_date=start_date)
                
                if data.empty:
                    stats['error'] = 'No data returned'
                    self.key_stats[api_key]['failures'] += 1
                    continue
                
                # Clean and validate data
                data['is_null'] = data['value'].isna().astype(int)
                data['value'] = pd.to_numeric(data['value'], errors='coerce')
                data['is_null'] = data['value'].isna().astype(int)
                
                cursor = conn.cursor()
                
                # Save indicator metadata
                cursor.execute('''
                INSERT OR REPLACE INTO indicators 
                (series_id, name, category, units, frequency, description, first_date, last_date, last_updated, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    series_id,
                    name,
                    category,
                    metadata.get('units', ''),
                    metadata.get('frequency', ''),
                    metadata.get('notes', ''),
                    data['date'].min().strftime('%Y-%m-%d') if not data.empty else None,
                    data['date'].max().strftime('%Y-%m-%d') if not data.empty else None,
                    datetime.now().isoformat(),
                    json.dumps(metadata)
                ))
                
                # Save observations
                saved_count = 0
                null_count = 0
                
                for _, row in data.iterrows():
                    try:
                        is_null = pd.isna(row['value'])
                        cursor.execute('''
                        INSERT OR REPLACE INTO observations
                        (series_id, date, value, is_null)
                        VALUES (?, ?, ?, ?)
                        ''', (
                            series_id,
                            row['date'].strftime('%Y-%m-%d'),
                            row['value'] if not is_null else None,
                            int(is_null)
                        ))
                        saved_count += 1
                        if is_null:
                            null_count += 1
                    except Exception as e:
                        logger.warning(f"    ⚠️ Error saving observation: {e}")
                
                conn.commit()
                
                # Success!
                stats['success'] = True
                stats['observations'] = saved_count
                stats['null_values'] = null_count
                self.key_stats[api_key]['successes'] += 1
                
                logger.info(f"    ✅ {saved_count} observations ({null_count} nulls) [Key: {api_key[:10]}...]")
                
                # Move to next key for load balancing
                self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
                
                # Small delay to be respectful
                time.sleep(0.1)
                break
                
            except Exception as e:
                error_msg = str(e)
                stats['error'] = error_msg
                
                # Handle rate limiting
                if '429' in error_msg or 'Too Many Requests' in error_msg:
                    logger.warning(f"    🚫 Rate limited on key {api_key[:10]}...")
                    self.key_stats[api_key]['rate_limits'] += 1
                    self.key_stats[api_key]['failures'] += 1
                    
                    # Put key in cooldown
                    cooldown_time = min(60 * (2 ** self.key_stats[api_key]['rate_limits']), 300)  # Max 5 min
                    self.key_stats[api_key]['cooldown_until'] = datetime.now() + timedelta(seconds=cooldown_time)
                    
                    logger.info(f"    ⏸️ Key {api_key[:10]}... in cooldown for {cooldown_time}s")
                    
                    # Try next key
                    self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
                    continue
                else:
                    logger.error(f"    ❌ Error with key {api_key[:10]}...: {error_msg}")
                    self.key_stats[api_key]['failures'] += 1
                    break
        
        return stats
    
    def print_key_stats(self):
        """Print statistics for each API key"""
        
        print(f"\n📊 API KEY PERFORMANCE:")
        for i, (api_key, stats) in enumerate(self.key_stats.items(), 1):
            success_rate = (stats['successes'] / stats['requests'] * 100) if stats['requests'] > 0 else 0
            print(f"  🔑 Key {i} ({api_key[:10]}...): {stats['requests']} requests, {stats['successes']} success ({success_rate:.1f}%), {stats['rate_limits']} rate limits")

# COMPREHENSIVE INDICATOR REGISTRY
EXHAUSTIVE_INDICATORS = {
    'interest_rates': {
        'FEDFUNDS': 'Federal Funds Rate',
        'DFEDTARU': 'Federal Funds Upper Target',
        'DFEDTARL': 'Federal Funds Lower Target',
        'DGS1MO': '1-Month Treasury Rate',
        'DGS3MO': '3-Month Treasury Rate',
        'DGS6MO': '6-Month Treasury Rate',
        'DGS1': '1-Year Treasury Rate',
        'DGS2': '2-Year Treasury Rate',
        'DGS3': '3-Year Treasury Rate',
        'DGS5': '5-Year Treasury Rate',
        'DGS7': '7-Year Treasury Rate',
        'DGS10': '10-Year Treasury Rate',
        'DGS20': '20-Year Treasury Rate',
        'DGS30': '30-Year Treasury Rate',
        'T10Y2Y': 'Yield Curve Spread (10Y-2Y)',
        'T10Y3M': 'Yield Curve Spread (10Y-3M)',
        'AAA': 'AAA Corporate Bond Yield',
        'BAA': 'BAA Corporate Bond Yield',
        'MORTGAGE30US': '30-Year Fixed Mortgage Rate',
        'MORTGAGE15US': '15-Year Fixed Mortgage Rate',
        'TEDRATE': 'TED Spread',
        'TB3MS': '3-Month Treasury Bill Rate',
        'TB6MS': '6-Month Treasury Bill Rate',
        'DPRIME': 'Bank Prime Loan Rate',
    },
    'economic_growth': {
        'GDP': 'Gross Domestic Product',
        'GDPC1': 'Real GDP',
        'GDPPOT': 'Real Potential GDP',
        'A191RL1Q225SBEA': 'GDP Growth Rate (QoQ)',
        'INDPRO': 'Industrial Production Index',
        'IPMAN': 'Manufacturing Production',
        'TCU': 'Total Capacity Utilization',
        'MCUMFN': 'Manufacturing Capacity Utilization',
        'CFNAI': 'Chicago Fed National Activity Index',
        'USSLIND': 'Leading Economic Index',
        'OPHNFB': 'Nonfarm Business Productivity',
        'ULCNFB': 'Unit Labor Costs',
        'NAPM': 'ISM Manufacturing PMI',
        'NAPMSII': 'ISM Services PMI',
    },
    'employment': {
        'UNRATE': 'Unemployment Rate',
        'U6RATE': 'U-6 Unemployment Rate',
        'PAYEMS': 'Total Nonfarm Payrolls',
        'USPRIV': 'Private Nonfarm Payrolls',
        'USGOVT': 'Government Employment',
        'MANEMP': 'Manufacturing Employment',
        'USCONS': 'Construction Employment',
        'CLF16OV': 'Civilian Labor Force',
        'CIVPART': 'Labor Force Participation Rate',
        'EMRATIO': 'Employment-Population Ratio',
        'ICSA': 'Initial Jobless Claims',
        'CCSA': 'Continuing Jobless Claims',
        'JTSJOL': 'Job Openings',
        'JTSQUR': 'Quits Rate',
        'JTSHIR': 'Hires Rate',
        'AHETPI': 'Average Hourly Earnings',
        'AWHAETP': 'Average Weekly Hours',
    },
    'inflation': {
        'CPIAUCSL': 'Consumer Price Index',
        'CPILFESL': 'Core CPI',
        'PPIACO': 'Producer Price Index',
        'PPILFE': 'Core PPI',
        'PCEPI': 'PCE Price Index',
        'PCEPILFE': 'Core PCE Price Index',
        'GDPDEF': 'GDP Deflator',
        'T5YIE': '5-Year Breakeven Inflation Rate',
        'T10YIE': '10-Year Breakeven Inflation Rate',
        'MICH': 'University of Michigan Inflation Expectations',
        'DFII10': '10-Year TIPS Rate',
        'DFII5': '5-Year TIPS Rate',
    },
    'consumer': {
        'PCE': 'Personal Consumption Expenditures',
        'PI': 'Personal Income',
        'DSPI': 'Disposable Personal Income',
        'PSAVERT': 'Personal Saving Rate',
        'RSAFS': 'Retail Sales',
        'RSAFSNA': 'Retail Sales Ex Auto',
        'TOTALSA': 'Motor Vehicle Sales',
        'UMCSENT': 'Consumer Sentiment',
        'TOTALSL': 'Consumer Credit',
        'DRCCLACBS': 'Credit Card Delinquency Rate',
    },
    'housing': {
        'HOUST': 'Housing Starts',
        'HOUST1F': 'Single-Family Housing Starts',
        'PERMIT': 'Building Permits',
        'HSN1F': 'New Home Sales',
        'EXHOSLUSM495S': 'Existing Home Sales',
        'MSPUS': 'Median Home Price',
        'CSUSHPINSA': 'Case-Shiller Home Price Index',
        'RHORUSQ156N': 'Home Ownership Rate',
        'TTLCONS': 'Construction Spending',
    },
    'business': {
        'BUSINV': 'Business Inventories',
        'DGORDER': 'Durable Goods Orders',
        'NEWORDER': 'Core Durable Goods Orders',
        'CP': 'Corporate Profits',
        'PHILLY': 'Philadelphia Fed Index',
    },
    'trade': {
        'BOPGSTB': 'Trade Balance',
        'EXPGS': 'Exports of Goods and Services',
        'IMPGS': 'Imports of Goods and Services',
        'DTWEXBGS': 'US Dollar Index',
        'DEXUSEU': 'EUR/USD Exchange Rate',
        'DEXUSUK': 'GBP/USD Exchange Rate',
    },
    'financial_markets': {
        'SP500': 'S&P 500',
        'NASDAQCOM': 'NASDAQ Composite',
        'DJIA': 'Dow Jones Industrial Average',
        'VIXCLS': 'VIX Volatility Index',
        'BAMLH0A0HYM2': 'High Yield Credit Spread',
        'BAMLC0A0CM': 'Investment Grade Credit Spread',
    },
    'banking': {
        'TOTBKCR': 'Total Bank Credit',
        'BUSLOANS': 'Commercial & Industrial Loans',
        'REALLN': 'Real Estate Loans',
        'TOTRESNS': 'Bank Reserves',
        'M1SL': 'M1 Money Supply',
        'M2SL': 'M2 Money Supply',
        'M2V': 'Velocity of M2',
    },
    'commodities': {
        'DCOILWTICO': 'WTI Crude Oil',
        'DHHNGSP': 'Natural Gas',
        'PCOPPUSDM': 'Copper Price',
        'PMAIZMTUSDM': 'Corn Price',
        'PWHEAMTUSDM': 'Wheat Price',
        'PSOYBUSDM': 'Soybeans Price',
    },
    'government': {
        'GFDEBTN': 'Federal Debt',
        'GFDEGDQ188S': 'Federal Debt to GDP',
        'FYFSGDA188S': 'Federal Surplus/Deficit',
        'FGRECPT': 'Federal Receipts',
        'FGEXPND': 'Federal Outlays',
    }
}

def exhaust_fred_with_rotation(db_path: str, start_date: str = '2000-01-01'):
    """
    Exhaustively collect FRED data using API key rotation
    """
    
    # Initialize rotating collector
    collector = RotatingFREDCollector()
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # Overall statistics
    overall_stats = {
        'start_time': datetime.now(),
        'categories': [],
        'total_indicators': 0,
        'successful_indicators': 0,
        'failed_indicators': 0,
        'total_observations': 0
    }
    
    total_indicators = sum(len(indicators) for indicators in EXHAUSTIVE_INDICATORS.values())
    
    logger.info(f"🚀 EXHAUSTING FRED API WITH KEY ROTATION!")
    logger.info(f"🔑 Using {len(collector.api_keys)} API keys for maximum throughput")
    logger.info(f"📊 Target: {total_indicators} indicators across {len(EXHAUSTIVE_INDICATORS)} categories")
    logger.info(f"📅 Start date: {start_date}")
    
    # Collect data by category
    for category_name, indicators in EXHAUSTIVE_INDICATORS.items():
        logger.info(f"\n🔄 CATEGORY: {category_name.upper()} ({len(indicators)} indicators)")
        
        category_stats = {
            'category': category_name,
            'total': len(indicators),
            'successful': 0,
            'failed': 0,
            'total_observations': 0,
            'indicators': []
        }
        
        for i, (series_id, name) in enumerate(indicators.items(), 1):
            logger.info(f"  [{i}/{len(indicators)}] {name} ({series_id})...")
            
            # Collect with rotation
            stats = collector.collect_with_rotation(
                series_id, name, category_name, conn, start_date
            )
            
            category_stats['indicators'].append(stats)
            
            if stats['success']:
                category_stats['successful'] += 1
                category_stats['total_observations'] += stats['observations']
            else:
                category_stats['failed'] += 1
        
        overall_stats['categories'].append(category_stats)
        overall_stats['total_indicators'] += category_stats['total']
        overall_stats['successful_indicators'] += category_stats['successful']
        overall_stats['failed_indicators'] += category_stats['failed']
        overall_stats['total_observations'] += category_stats['total_observations']
        
        success_rate = (category_stats['successful'] / category_stats['total'] * 100) if category_stats['total'] > 0 else 0
        logger.info(f"✅ {category_name}: {category_stats['successful']}/{category_stats['total']} ({success_rate:.1f}%) - {category_stats['total_observations']:,} obs")
    
    overall_stats['end_time'] = datetime.now()
    overall_stats['duration'] = (overall_stats['end_time'] - overall_stats['start_time']).total_seconds()
    
    conn.close()
    
    # Print API key performance
    collector.print_key_stats()
    
    return overall_stats

def main():
    """Main exhaustive collection with rotation"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators_exhaustive.db')
    
    print("🚀 FRED API EXHAUSTIVE COLLECTION WITH KEY ROTATION")
    print("=" * 100)
    print("🔑 Using 3 API keys with intelligent rotation")
    print("📊 Target: 150+ comprehensive macro economic indicators")
    print("⚡ Optimized for maximum throughput and minimal rate limiting")
    print("=" * 100)
    
    # Create fresh database
    if os.path.exists(db_path):
        backup_path = db_path.replace('.db', f'_backup_{int(time.time())}.db')
        os.rename(db_path, backup_path)
        logger.info(f"📦 Backed up existing database to: {backup_path}")
    
    # Create clean database
    from fix_data_quality import create_clean_database
    create_clean_database(db_path)
    
    # Start exhaustive collection
    stats = exhaust_fred_with_rotation(db_path, start_date='2000-01-01')
    
    # Print final summary
    print("\n" + "=" * 100)
    print("🎯 EXHAUSTIVE COLLECTION WITH ROTATION COMPLETED!")
    print("=" * 100)
    
    success_rate = (stats['successful_indicators'] / stats['total_indicators'] * 100) if stats['total_indicators'] > 0 else 0
    
    print(f"🏆 FINAL RESULTS:")
    print(f"  📊 Total indicators: {stats['total_indicators']}")
    print(f"  ✅ Successfully collected: {stats['successful_indicators']}")
    print(f"  ❌ Failed: {stats['failed_indicators']}")
    print(f"  📈 Success rate: {success_rate:.1f}%")
    print(f"  📋 Total observations: {stats['total_observations']:,}")
    print(f"  ⏱️ Duration: {stats['duration']/60:.1f} minutes")
    
    print(f"\n✅ EXHAUSTIVE DATABASE READY!")
    print(f"📊 Database: {db_path}")
    print(f"🎯 Ready for comprehensive macro analysis!")

if __name__ == "__main__":
    main()
