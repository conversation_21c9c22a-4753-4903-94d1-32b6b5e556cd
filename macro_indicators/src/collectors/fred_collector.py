"""
FRED Data Collector

This module handles data collection from the Federal Reserve Economic Data (FRED) API.
It provides comprehensive US economic indicators.
"""

import os
import logging
import pandas as pd
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
import sqlite3
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FREDCollector:
    """
    Collector for Federal Reserve Economic Data (FRED).
    
    This class handles authentication and data collection from the FRED API.
    """
    
    # FRED API base URL
    BASE_URL = "https://api.stlouisfed.org/fred"
    
    # Key US economic indicators with their FRED series IDs
    US_INDICATORS = {
        # Interest Rates & Monetary Policy
        'Federal Funds Rate': 'FEDFUNDS',
        '10-Year Treasury Yield': 'DGS10',
        '2-Year Treasury Yield': 'DGS2',
        'Yield Curve Spread (10Y-2Y)': 'T10Y2Y',
        'Real Interest Rate': 'REAINTRATREARAT10Y',
        
        # Economic Growth & Activity
        'GDP Growth Rate': 'A191RL1Q225SBEA',
        'GDP': 'GDP',
        'Unemployment Rate': 'UNRATE',
        'Non-Farm Payrolls': 'PAYEMS',
        'Manufacturing PMI': 'NAPM',
        'Consumer Confidence': 'UMCSENT',
        'Retail Sales': 'RSAFS',
        'Industrial Production': 'INDPRO',
        
        # Inflation & Prices
        'Consumer Price Index': 'CPIAUCSL',
        'Core CPI': 'CPILFESL',
        'Producer Price Index': 'PPIACO',
        'PCE Price Index': 'PCEPI',
        'Core PCE': 'PCEPILFE',
        
        # Housing & Construction
        'Housing Starts': 'HOUST',
        'New Home Sales': 'HSN1F',
        'Existing Home Sales': 'EXHOSLUSM495S',
        'Case-Shiller Home Price Index': 'CSUSHPINSA',
        
        # Trade & International
        'Trade Balance': 'BOPGSTB',
        'Current Account Balance': 'NETFI',
        'US Dollar Index': 'DTWEXBGS',
        'Imports': 'IMPGS',
        'Exports': 'EXPGS',
        
        # Business & Corporate
        'Corporate Profits': 'CP',
        'Business Inventories': 'BUSINV',
        'Capacity Utilization': 'TCU',
        'Durable Goods Orders': 'DGORDER',
        
        # Financial Markets
        'S&P 500': 'SP500',
        'VIX Volatility Index': 'VIXCLS',
        'Commercial Bank Credit': 'TOTBKCR',
        'M2 Money Supply': 'M2SL',
    }
    
    def __init__(self, api_key: Optional[str] = None, db_path: Optional[str] = None):
        """
        Initialize the FRED collector.
        
        Args:
            api_key: FRED API key. If None, will try to get from environment.
            db_path: Path to SQLite database for storing data.
        """
        self.api_key = api_key or os.getenv('FRED')
        if not self.api_key:
            raise ValueError("FRED API key not provided and not found in environment variables")
        
        self.db_path = db_path
        if self.db_path:
            self._init_database()
        
        logger.info("Initialized FRED collector")
    
    def _init_database(self):
        """Initialize the SQLite database for storing indicator data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create tables if they don't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                series_id TEXT NOT NULL,
                name TEXT NOT NULL,
                category TEXT,
                units TEXT,
                frequency TEXT,
                description TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                UNIQUE(series_id)
            )
            ''')
            
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS indicator_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                series_id TEXT NOT NULL,
                date DATE NOT NULL,
                value REAL,
                UNIQUE(series_id, date)
            )
            ''')
            
            conn.commit()
            conn.close()
            logger.info(f"Database initialized at {self.db_path}")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def get_series_info(self, series_id: str) -> Dict:
        """
        Get metadata for a specific FRED series.
        
        Args:
            series_id: FRED series ID
            
        Returns:
            Dictionary with series metadata
        """
        url = f"{self.BASE_URL}/series"
        
        params = {
            'series_id': series_id,
            'api_key': self.api_key,
            'file_type': 'json'
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if 'seriess' in data and len(data['seriess']) > 0:
                return data['seriess'][0]
            else:
                logger.warning(f"No information found for series {series_id}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting series info for {series_id}: {str(e)}")
            return {}
    
    def get_series_data(self, series_id: str, start_date: Optional[str] = None, 
                      end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Get data for a specific FRED series.
        
        Args:
            series_id: FRED series ID
            start_date: Start date in format 'YYYY-MM-DD'
            end_date: End date in format 'YYYY-MM-DD'
            
        Returns:
            DataFrame with series data
        """
        # Set default dates if not provided
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        if start_date is None:
            # Default to 10 years of data
            start_date = (datetime.now() - timedelta(days=365*10)).strftime('%Y-%m-%d')
        
        # Build request URL
        url = f"{self.BASE_URL}/series/observations"
        
        params = {
            'series_id': series_id,
            'api_key': self.api_key,
            'file_type': 'json',
            'observation_start': start_date,
            'observation_end': end_date,
            'sort_order': 'desc',
            'units': 'lin'  # Linear units (no transformation)
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if 'observations' in data:
                # Convert to DataFrame
                df = pd.DataFrame(data['observations'])
                # Convert date strings to datetime
                df['date'] = pd.to_datetime(df['date'])
                # Convert values to float where possible
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
                
                return df
            else:
                logger.warning(f"No observations found for series {series_id}")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error getting data for {series_id}: {str(e)}")
            return pd.DataFrame()
    
    def save_to_database(self, series_id: str, name: str, data: pd.DataFrame, 
                       metadata: Dict = None, category: str = None):
        """
        Save series data to SQLite database.
        
        Args:
            series_id: FRED series ID
            name: Indicator name
            data: DataFrame with series data
            metadata: Dictionary with series metadata
            category: Indicator category
        """
        if not self.db_path:
            logger.warning("Database path not provided, skipping save")
            return
        
        if data.empty:
            logger.warning(f"No data to save for {series_id}")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Save indicator metadata
            cursor.execute('''
            INSERT OR REPLACE INTO indicators 
            (series_id, name, category, units, frequency, description, metadata, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                series_id,
                name,
                category,
                metadata.get('units', '') if metadata else '',
                metadata.get('frequency', '') if metadata else '',
                metadata.get('notes', '') if metadata else '',
                json.dumps(metadata) if metadata else '{}'
            ))
            
            # Save indicator data
            for _, row in data.iterrows():
                cursor.execute('''
                INSERT OR REPLACE INTO indicator_data
                (series_id, date, value)
                VALUES (?, ?, ?)
                ''', (
                    series_id,
                    row['date'].strftime('%Y-%m-%d'),
                    row['value']
                ))
            
            conn.commit()
            conn.close()
            logger.info(f"Saved {len(data)} observations for {name} ({series_id})")
        except Exception as e:
            logger.error(f"Error saving data for {series_id}: {str(e)}")
    
    def collect_us_indicators(self, indicators: Dict[str, str] = None, 
                            start_date: Optional[str] = None,
                            save_to_db: bool = True) -> Dict[str, pd.DataFrame]:
        """
        Collect data for US economic indicators.
        
        Args:
            indicators: Dictionary mapping indicator names to FRED series IDs.
                       If None, uses the default US_INDICATORS.
            start_date: Start date for data collection in format 'YYYY-MM-DD'.
                       If None, defaults to 10 years ago.
            save_to_db: Whether to save data to database.
            
        Returns:
            Dictionary mapping indicator names to DataFrames with indicator data.
        """
        if indicators is None:
            indicators = self.US_INDICATORS
        
        results = {}
        
        for name, series_id in indicators.items():
            logger.info(f"Collecting data for {name} ({series_id})...")
            
            # Get series metadata
            metadata = self.get_series_info(series_id)
            
            # Get series data
            data = self.get_series_data(series_id, start_date=start_date)
            
            if not data.empty:
                results[name] = data
                logger.info(f"Collected {len(data)} observations for {name}")
                
                # Determine category based on indicator name
                category = None
                if 'Rate' in name or 'Treasury' in name or 'Yield' in name:
                    category = 'interest_rates'
                elif 'GDP' in name or 'Production' in name or 'Payrolls' in name:
                    category = 'economic_growth'
                elif 'CPI' in name or 'Price' in name or 'Inflation' in name:
                    category = 'inflation'
                elif 'Dollar' in name or 'Trade' in name or 'Export' in name or 'Import' in name:
                    category = 'currency'
                elif 'Housing' in name or 'Home' in name:
                    category = 'housing'
                elif 'S&P' in name or 'VIX' in name:
                    category = 'market_sentiment'
                
                # Save to database if requested
                if save_to_db and self.db_path:
                    self.save_to_database(series_id, name, data, metadata, category)
            else:
                logger.warning(f"No data collected for {name}")
        
        return results
