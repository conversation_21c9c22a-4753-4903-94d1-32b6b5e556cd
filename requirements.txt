# Data collection
yfinance==0.2.65
pandas-datareader==0.10.0
alpha-vantage==2.3.1

# Time-series database
influxdb-client==1.37.0

# Technical analysis
ta-lib==0.4.26
pandas-ta==0.3.14b

# Machine learning
scikit-learn==1.3.0
xgboost==1.7.5
lightgbm==4.0.0
imbalanced-learn==0.11.0

# Deep learning for transformers/LSTM
tensorflow==2.13.0
torch==2.0.1
transformers==4.30.0

# Advanced risk metrics
pyfolio==0.9.2
empyrical==0.5.5

# Regime detection and volatility modeling
hmmlearn==0.3.0
arch==5.3.1

# Sentiment analysis
textblob==0.17.1
vaderSentiment==3.3.2

# Visualization & Web Frontend
matplotlib==3.7.1
seaborn==0.12.2
plotly==5.15.0
streamlit==1.25.0
dash==2.11.1
dash-bootstrap-components==1.4.1

# Utilities
numpy==1.24.3
pandas==2.0.3
requests==2.31.0
