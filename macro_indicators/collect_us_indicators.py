#!/usr/bin/env python3
"""
Comprehensive US Economic Indicator Collection Script

This script collects a wide range of US economic indicators from FRED
and stores them in a SQLite database with proper schema.
"""

import os
import sys
import logging
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.collectors.fred_collector import FREDCollector
from src.database.db_manager import DatabaseManager
from src.registry.indicator_registry import (
    US_MACRO_INDICATORS, 
    get_all_indicators,
    get_indicators_by_category,
    get_category_list,
    REGISTRY_SUMMARY
)
from src.schema.data_schema import MacroIndicator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'logs', 'collection.log'), 'a')
    ]
)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs(os.path.join(os.path.dirname(__file__), 'logs'), exist_ok=True)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Collect US economic indicators from FRED')
    
    parser.add_argument(
        '--categories',
        type=str,
        nargs='+',
        choices=get_category_list() + ['all'],
        default=['all'],
        help='Categories of indicators to collect (default: all)'
    )
    
    parser.add_argument(
        '--indicators',
        type=str,
        nargs='+',
        help='Specific indicators to collect (by FRED series ID)'
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        default='2000-01-01',
        help='Start date for data collection (YYYY-MM-DD, default: 2000-01-01)'
    )
    
    parser.add_argument(
        '--db-path',
        type=str,
        default=os.path.join(os.path.dirname(__file__), 'data', 'macro_indicators.db'),
        help='Path to SQLite database'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=None,
        help='Limit number of indicators to collect (for testing)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser.parse_args()

def collect_indicators(categories: List[str], 
                     specific_indicators: Optional[List[str]] = None,
                     start_date: str = '2000-01-01',
                     db_path: str = None,
                     limit: Optional[int] = None,
                     verbose: bool = False) -> Dict[str, int]:
    """
    Collect indicators from specified categories.
    
    Args:
        categories: List of categories to collect
        specific_indicators: List of specific indicators to collect (by FRED series ID)
        start_date: Start date for data collection
        db_path: Path to SQLite database
        limit: Limit number of indicators to collect
        verbose: Enable verbose logging
        
    Returns:
        Dictionary with collection statistics
    """
    # Set up collectors and database
    fred_collector = FREDCollector()
    db_manager = DatabaseManager(db_path)
    
    # Determine which indicators to collect
    indicators_to_collect = {}
    
    if 'all' in categories:
        # Collect all indicators
        indicators_to_collect = get_all_indicators()
        logger.info(f"Collecting all {len(indicators_to_collect)} indicators")
    else:
        # Collect indicators from specified categories
        for category in categories:
            category_indicators = get_indicators_by_category(category)
            indicators_to_collect.update(category_indicators)
            logger.info(f"Added {len(category_indicators)} indicators from category '{category}'")
    
    # Filter to specific indicators if provided
    if specific_indicators:
        filtered_indicators = {}
        for name, series_id in indicators_to_collect.items():
            if series_id in specific_indicators:
                filtered_indicators[name] = series_id
        
        indicators_to_collect = filtered_indicators
        logger.info(f"Filtered to {len(indicators_to_collect)} specific indicators")
    
    # Apply limit if specified
    if limit and limit < len(indicators_to_collect):
        # Convert to list of tuples, limit, then convert back to dict
        items = list(indicators_to_collect.items())[:limit]
        indicators_to_collect = dict(items)
        logger.info(f"Limited to {len(indicators_to_collect)} indicators")
    
    # Collection statistics
    stats = {
        'total_indicators': len(indicators_to_collect),
        'successful_indicators': 0,
        'failed_indicators': 0,
        'total_observations': 0,
        'start_time': datetime.now(),
    }
    
    # Collect indicators
    logger.info(f"Starting collection of {len(indicators_to_collect)} indicators from {start_date}")
    
    for i, (name, series_id) in enumerate(indicators_to_collect.items(), 1):
        try:
            logger.info(f"[{i}/{len(indicators_to_collect)}] Collecting {name} ({series_id})...")
            
            # Get series metadata
            metadata = fred_collector.get_series_info(series_id)
            
            if not metadata:
                logger.warning(f"⚠️ No metadata found for {series_id}, skipping")
                stats['failed_indicators'] += 1
                continue
            
            # Create indicator object
            category = None
            for cat, indicators in US_MACRO_INDICATORS.items():
                if name in indicators:
                    category = cat
                    break
            
            indicator = MacroIndicator(
                indicator_id=series_id,
                name=name,
                category=category or 'uncategorized',
                description=metadata.get('notes', ''),
                units=metadata.get('units', ''),
                frequency=metadata.get('frequency', ''),
                source='FRED',
                source_url=f"https://fred.stlouisfed.org/series/{series_id}",
                metadata=metadata
            )
            
            # Save indicator metadata
            db_manager.save_indicator(indicator)
            
            # Get series data
            data = fred_collector.get_series_data(series_id, start_date=start_date)
            
            if data.empty:
                logger.warning(f"⚠️ No data found for {series_id}")
                stats['failed_indicators'] += 1
                continue
            
            # Save observations
            observation_count = db_manager.save_dataframe_observations(
                indicator_id=series_id,
                df=data
            )
            
            # Update indicator with latest value
            if not data.empty:
                latest_row = data.iloc[0]
                db_manager.update_indicator_latest_value(
                    indicator_id=series_id,
                    latest_value=latest_row['value'],
                    latest_date=latest_row['date']
                )
            
            # Update statistics
            stats['successful_indicators'] += 1
            stats['total_observations'] += observation_count
            
            if verbose:
                logger.info(f"✅ Collected {observation_count} observations for {name}")
            
        except Exception as e:
            logger.error(f"❌ Error collecting {name} ({series_id}): {str(e)}")
            stats['failed_indicators'] += 1
    
    # Calculate end time and duration
    stats['end_time'] = datetime.now()
    stats['duration_seconds'] = (stats['end_time'] - stats['start_time']).total_seconds()
    
    return stats

def print_collection_summary(stats: Dict[str, int]):
    """Print summary of collection statistics"""
    print("\n" + "=" * 50)
    print("📊 COLLECTION SUMMARY")
    print("=" * 50)
    
    # Calculate success rate
    success_rate = (stats['successful_indicators'] / stats['total_indicators']) * 100 if stats['total_indicators'] > 0 else 0
    
    print(f"📈 Indicators:")
    print(f"  • Total: {stats['total_indicators']}")
    print(f"  • Successful: {stats['successful_indicators']}")
    print(f"  • Failed: {stats['failed_indicators']}")
    print(f"  • Success Rate: {success_rate:.1f}%")
    
    print(f"\n📊 Observations:")
    print(f"  • Total: {stats['total_observations']}")
    print(f"  • Average per indicator: {stats['total_observations'] / stats['successful_indicators']:.1f}" if stats['successful_indicators'] > 0 else "  • Average per indicator: N/A")
    
    print(f"\n⏱️ Performance:")
    print(f"  • Start time: {stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  • End time: {stats['end_time'].strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  • Duration: {stats['duration_seconds']:.1f} seconds")
    print(f"  • Average time per indicator: {stats['duration_seconds'] / stats['total_indicators']:.1f} seconds" if stats['total_indicators'] > 0 else "  • Average time per indicator: N/A")
    
    print("\n" + "=" * 50)

def main():
    """Main function"""
    # Parse command line arguments
    args = parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Print registry summary
    print("\n" + "=" * 50)
    print("📊 INDICATOR REGISTRY SUMMARY")
    print("=" * 50)
    print(f"Total indicators: {REGISTRY_SUMMARY['total_indicators']}")
    print(f"Categories: {REGISTRY_SUMMARY['categories']}")
    print("\nIndicators per category:")
    for category, count in REGISTRY_SUMMARY['indicators_per_category'].items():
        print(f"  • {category}: {count}")
    print("=" * 50)
    
    # Collect indicators
    stats = collect_indicators(
        categories=args.categories,
        specific_indicators=args.indicators,
        start_date=args.start_date,
        db_path=args.db_path,
        limit=args.limit,
        verbose=args.verbose
    )
    
    # Print collection summary
    print_collection_summary(stats)
    
    # Print database stats
    db_manager = DatabaseManager(args.db_path)
    db_stats = db_manager.get_database_stats()
    
    print("\n" + "=" * 50)
    print("📊 DATABASE SUMMARY")
    print("=" * 50)
    print(f"Indicators: {db_stats['indicator_count']}")
    print(f"Observations: {db_stats['observation_count']}")
    print(f"Database size: {db_stats['database_size_mb']:.2f} MB")
    print(f"Database path: {db_stats['database_path']}")
    
    print("\nIndicators per category:")
    for category, count in db_stats.get('category_counts', {}).items():
        print(f"  • {category}: {count}")
    
    print("\n" + "=" * 50)
    print("✅ Collection complete!")

if __name__ == "__main__":
    main()
